package com.zxy.product.exam.web.controller;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.product.exam.api.ExamService;
import com.zxy.product.exam.api.PaperInstanceService;
import com.zxy.product.exam.api.QuestionCopyService;
import com.zxy.product.exam.entity.ExamRecord;
import com.zxy.product.exam.entity.PaperInstance;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: xxh
 * @Date: 2025/7/28 - 07 - 28 - 10:03
 * @Description: com.zxy.product.exam.web.controller
 * @version: 1.0
 */
@Controller
@RequestMapping("/new-employee")
public class ExamNewEmployeeController {

//    @Value("${new.employee.exam.id}")
//    private String examId;

    private ExamService examService;

    private QuestionCopyService questionCopyService;

    private PaperInstanceService paperInstanceService;

    @Autowired
    public void setPaperInstanceService(PaperInstanceService paperInstanceService) {
        this.paperInstanceService = paperInstanceService;
    }

    @Autowired
    public void setExamService(ExamService examService) {
        this.examService = examService;
    }

    @Autowired
    public void setQuestionCopyService(QuestionCopyService questionCopyService) {
        this.questionCopyService = questionCopyService;
    }

    @RequestMapping(value = "/score-detail", method = RequestMethod.GET)
    @Param()
    @JSON("*")
    @JSON("*.*")
    @JSON("*.*.*")
    public Map<String, ExamRecord> getWithScoreDetail(RequestContext requestContext) {
        String examId = "b6e815c4-3048-466c-be41-d056824f53a0";
        List<PaperInstance> paperInstanceByExamId = paperInstanceService.getAllPaperInstanceByExamId(examId);
        String paperInstanceId = "";
        if(CollectionUtils.isNotEmpty(paperInstanceByExamId)){
            paperInstanceId = paperInstanceByExamId.get(0).getId();
            return questionCopyService.findQuestionsByPaperAndExamId(paperInstanceId, examId, Arrays.asList(new String[]{"1","12cc152a-bd89-4789-b575-79783c3a3fff"}));
        }
       return new HashMap<>();
    }


    @RequestMapping(value = "/findNewEmployeeExam", method = RequestMethod.GET)
    @Param(name = "startTime", type = Long.class)
    @Param(name = "endTime", type = Long.class)
    @Param(name = "examId", type = String.class)
    @Param(name = "paperInstanceId", type = String.class)
    @Param(name = "page", type = Integer.class)
    @Param(name = "pageSize", type = Integer.class)
    @JSON("*")
    @JSON("*.*")
    @JSON("*.*.*")
    public Map<String, String> findNewEmployeeExam(RequestContext requestContext){
        Long startTime = requestContext.getLong("startTime");
        Long endTime = requestContext.getLong("endTime");
        String examId = requestContext.getString("examId");
        String paperInstanceId = requestContext.getString("paperInstanceId");
        Integer page = requestContext.getInteger("page");
        Integer pageSize = requestContext.getInteger("pageSize");
        List<String> memberId = questionCopyService.findNewEmployeeExam(startTime,endTime, examId, paperInstanceId,page,pageSize);
        return ImmutableMap.of("result", "sucess");
    }
}

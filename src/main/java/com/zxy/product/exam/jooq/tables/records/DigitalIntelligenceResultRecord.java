/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.exam.jooq.tables.records;


import com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult;
import com.zxy.product.exam.jooq.tables.interfaces.IDigitalIntelligenceResult;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record11;
import org.jooq.Row11;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 数智测评结果表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.12.4"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DigitalIntelligenceResultRecord extends UpdatableRecordImpl<DigitalIntelligenceResultRecord> implements Record11<String, String, String, String, String, String, String, String, String, Integer, Long>, IDigitalIntelligenceResult {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_id</code>. 记录ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_id</code>. 记录ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_name</code>. 用户名称
     */
    @Override
    public void setName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_name</code>. 用户名称
     */
    @Override
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_member_id</code>. 用户id
     */
    @Override
    public void setMemberId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_member_id</code>. 用户id
     */
    @Override
    public String getMemberId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_ability_tags</code>. 能力标签，多个中间用 | 隔开
     */
    @Override
    public void setAbilityTags(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_ability_tags</code>. 能力标签，多个中间用 | 隔开
     */
    @Override
    public String getAbilityTags() {
        return (String) get(3);
    }

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_strength_enhancement</code>. 优势强化，多个中间用 | 隔开
     */
    @Override
    public void setStrengthEnhancement(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_strength_enhancement</code>. 优势强化，多个中间用 | 隔开
     */
    @Override
    public String getStrengthEnhancement() {
        return (String) get(4);
    }

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_skill_improvement</code>. 短板提升，多个中间用 | 隔开
     */
    @Override
    public void setSkillImprovement(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_skill_improvement</code>. 短板提升，多个中间用 | 隔开
     */
    @Override
    public String getSkillImprovement() {
        return (String) get(5);
    }

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_short_term_plan</code>. 推荐发展路径--短期，多个中间用 | 隔开
     */
    @Override
    public void setShortTermPlan(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_short_term_plan</code>. 推荐发展路径--短期，多个中间用 | 隔开
     */
    @Override
    public String getShortTermPlan() {
        return (String) get(6);
    }

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_medium_term_plan</code>. 推荐发展路径--长期，多个中间用 | 隔开
     */
    @Override
    public void setMediumTermPlan(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_medium_term_plan</code>. 推荐发展路径--长期，多个中间用 | 隔开
     */
    @Override
    public String getMediumTermPlan() {
        return (String) get(7);
    }

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_recommended_courses_id</code>. 推荐课程，多个 id 中间用 , 隔开
     */
    @Override
    public void setRecommendedCoursesId(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_recommended_courses_id</code>. 推荐课程，多个 id 中间用 , 隔开
     */
    @Override
    public String getRecommendedCoursesId() {
        return (String) get(8);
    }

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_is_view</code>. 是否查看过结果:0 没有；1 已经查看
     */
    @Override
    public void setIsView(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_is_view</code>. 是否查看过结果:0 没有；1 已经查看
     */
    @Override
    public Integer getIsView() {
        return (Integer) get(9);
    }

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(10);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record11 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row11<String, String, String, String, String, String, String, String, String, Integer, Long> fieldsRow() {
        return (Row11) super.fieldsRow();
    }

    @Override
    public Row11<String, String, String, String, String, String, String, String, String, Integer, Long> valuesRow() {
        return (Row11) super.valuesRow();
    }

    @Override
    public Field<String> field1() {
        return DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.ID;
    }

    @Override
    public Field<String> field2() {
        return DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.NAME;
    }

    @Override
    public Field<String> field3() {
        return DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.MEMBER_ID;
    }

    @Override
    public Field<String> field4() {
        return DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.ABILITY_TAGS;
    }

    @Override
    public Field<String> field5() {
        return DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.STRENGTH_ENHANCEMENT;
    }

    @Override
    public Field<String> field6() {
        return DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.SKILL_IMPROVEMENT;
    }

    @Override
    public Field<String> field7() {
        return DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.SHORT_TERM_PLAN;
    }

    @Override
    public Field<String> field8() {
        return DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.MEDIUM_TERM_PLAN;
    }

    @Override
    public Field<String> field9() {
        return DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.RECOMMENDED_COURSES_ID;
    }

    @Override
    public Field<Integer> field10() {
        return DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.IS_VIEW;
    }

    @Override
    public Field<Long> field11() {
        return DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.CREATE_TIME;
    }

    @Override
    public String value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getName();
    }

    @Override
    public String value3() {
        return getMemberId();
    }

    @Override
    public String value4() {
        return getAbilityTags();
    }

    @Override
    public String value5() {
        return getStrengthEnhancement();
    }

    @Override
    public String value6() {
        return getSkillImprovement();
    }

    @Override
    public String value7() {
        return getShortTermPlan();
    }

    @Override
    public String value8() {
        return getMediumTermPlan();
    }

    @Override
    public String value9() {
        return getRecommendedCoursesId();
    }

    @Override
    public Integer value10() {
        return getIsView();
    }

    @Override
    public Long value11() {
        return getCreateTime();
    }

    @Override
    public DigitalIntelligenceResultRecord value1(String value) {
        setId(value);
        return this;
    }

    @Override
    public DigitalIntelligenceResultRecord value2(String value) {
        setName(value);
        return this;
    }

    @Override
    public DigitalIntelligenceResultRecord value3(String value) {
        setMemberId(value);
        return this;
    }

    @Override
    public DigitalIntelligenceResultRecord value4(String value) {
        setAbilityTags(value);
        return this;
    }

    @Override
    public DigitalIntelligenceResultRecord value5(String value) {
        setStrengthEnhancement(value);
        return this;
    }

    @Override
    public DigitalIntelligenceResultRecord value6(String value) {
        setSkillImprovement(value);
        return this;
    }

    @Override
    public DigitalIntelligenceResultRecord value7(String value) {
        setShortTermPlan(value);
        return this;
    }

    @Override
    public DigitalIntelligenceResultRecord value8(String value) {
        setMediumTermPlan(value);
        return this;
    }

    @Override
    public DigitalIntelligenceResultRecord value9(String value) {
        setRecommendedCoursesId(value);
        return this;
    }

    @Override
    public DigitalIntelligenceResultRecord value10(Integer value) {
        setIsView(value);
        return this;
    }

    @Override
    public DigitalIntelligenceResultRecord value11(Long value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public DigitalIntelligenceResultRecord values(String value1, String value2, String value3, String value4, String value5, String value6, String value7, String value8, String value9, Integer value10, Long value11) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    @Override
    public void from(IDigitalIntelligenceResult from) {
        setId(from.getId());
        setName(from.getName());
        setMemberId(from.getMemberId());
        setAbilityTags(from.getAbilityTags());
        setStrengthEnhancement(from.getStrengthEnhancement());
        setSkillImprovement(from.getSkillImprovement());
        setShortTermPlan(from.getShortTermPlan());
        setMediumTermPlan(from.getMediumTermPlan());
        setRecommendedCoursesId(from.getRecommendedCoursesId());
        setIsView(from.getIsView());
        setCreateTime(from.getCreateTime());
    }

    @Override
    public <E extends IDigitalIntelligenceResult> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached DigitalIntelligenceResultRecord
     */
    public DigitalIntelligenceResultRecord() {
        super(DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT);
    }

    /**
     * Create a detached, initialised DigitalIntelligenceResultRecord
     */
    public DigitalIntelligenceResultRecord(String id, String name, String memberId, String abilityTags, String strengthEnhancement, String skillImprovement, String shortTermPlan, String mediumTermPlan, String recommendedCoursesId, Integer isView, Long createTime) {
        super(DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT);

        set(0, id);
        set(1, name);
        set(2, memberId);
        set(3, abilityTags);
        set(4, strengthEnhancement);
        set(5, skillImprovement);
        set(6, shortTermPlan);
        set(7, mediumTermPlan);
        set(8, recommendedCoursesId);
        set(9, isView);
        set(10, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.exam.jooq.tables.pojos.DigitalIntelligenceResultEntity)) {
            return false;
        }
        com.zxy.product.exam.jooq.tables.pojos.DigitalIntelligenceResultEntity pojo = (com.zxy.product.exam.jooq.tables.pojos.DigitalIntelligenceResultEntity)source;
        pojo.into(this);
        return true;
    }
}

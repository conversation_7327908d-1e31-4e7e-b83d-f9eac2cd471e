/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.exam.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.exam.jooq.tables.interfaces.IDigitalIntelligenceResult;

import javax.annotation.Generated;


/**
 * 数智测评结果表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.12.4"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DigitalIntelligenceResultEntity extends BaseEntity implements IDigitalIntelligenceResult {

    private static final long serialVersionUID = 1L;

    private String name;
    private String memberId;
    private String abilityTags;
    private String strengthEnhancement;
    private String skillImprovement;
    private String shortTermPlan;
    private String mediumTermPlan;
    private String recommendedCoursesId;
    private Integer isView;

    public DigitalIntelligenceResultEntity() {}

    public DigitalIntelligenceResultEntity(IDigitalIntelligenceResult value) {
        this.name = value.getName();
        this.memberId = value.getMemberId();
        this.abilityTags = value.getAbilityTags();
        this.strengthEnhancement = value.getStrengthEnhancement();
        this.skillImprovement = value.getSkillImprovement();
        this.shortTermPlan = value.getShortTermPlan();
        this.mediumTermPlan = value.getMediumTermPlan();
        this.recommendedCoursesId = value.getRecommendedCoursesId();
        this.isView = value.getIsView();
    }

    public DigitalIntelligenceResultEntity(
        String id,
        String name,
        String memberId,
        String abilityTags,
        String strengthEnhancement,
        String skillImprovement,
        String shortTermPlan,
        String mediumTermPlan,
        String recommendedCoursesId,
        Integer isView,
        Long   createTime
    ) {
        super.setId(id);
        this.name = name;
        this.memberId = memberId;
        this.abilityTags = abilityTags;
        this.strengthEnhancement = strengthEnhancement;
        this.skillImprovement = skillImprovement;
        this.shortTermPlan = shortTermPlan;
        this.mediumTermPlan = mediumTermPlan;
        this.recommendedCoursesId = recommendedCoursesId;
        this.isView = isView;
        super.setCreateTime(createTime);
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getAbilityTags() {
        return this.abilityTags;
    }

    @Override
    public void setAbilityTags(String abilityTags) {
        this.abilityTags = abilityTags;
    }

    @Override
    public String getStrengthEnhancement() {
        return this.strengthEnhancement;
    }

    @Override
    public void setStrengthEnhancement(String strengthEnhancement) {
        this.strengthEnhancement = strengthEnhancement;
    }

    @Override
    public String getSkillImprovement() {
        return this.skillImprovement;
    }

    @Override
    public void setSkillImprovement(String skillImprovement) {
        this.skillImprovement = skillImprovement;
    }

    @Override
    public String getShortTermPlan() {
        return this.shortTermPlan;
    }

    @Override
    public void setShortTermPlan(String shortTermPlan) {
        this.shortTermPlan = shortTermPlan;
    }

    @Override
    public String getMediumTermPlan() {
        return this.mediumTermPlan;
    }

    @Override
    public void setMediumTermPlan(String mediumTermPlan) {
        this.mediumTermPlan = mediumTermPlan;
    }

    @Override
    public String getRecommendedCoursesId() {
        return this.recommendedCoursesId;
    }

    @Override
    public void setRecommendedCoursesId(String recommendedCoursesId) {
        this.recommendedCoursesId = recommendedCoursesId;
    }

    @Override
    public Integer getIsView() {
        return this.isView;
    }

    @Override
    public void setIsView(Integer isView) {
        this.isView = isView;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("DigitalIntelligenceResultEntity (");

        sb.append(getId());
        sb.append(", ").append(name);
        sb.append(", ").append(memberId);
        sb.append(", ").append(abilityTags);
        sb.append(", ").append(strengthEnhancement);
        sb.append(", ").append(skillImprovement);
        sb.append(", ").append(shortTermPlan);
        sb.append(", ").append(mediumTermPlan);
        sb.append(", ").append(recommendedCoursesId);
        sb.append(", ").append(isView);
        sb.append(", ").append(getCreateTime());

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    @Override
    public void from(IDigitalIntelligenceResult from) {
        setId(from.getId());
        setName(from.getName());
        setMemberId(from.getMemberId());
        setAbilityTags(from.getAbilityTags());
        setStrengthEnhancement(from.getStrengthEnhancement());
        setSkillImprovement(from.getSkillImprovement());
        setShortTermPlan(from.getShortTermPlan());
        setMediumTermPlan(from.getMediumTermPlan());
        setRecommendedCoursesId(from.getRecommendedCoursesId());
        setIsView(from.getIsView());
        setCreateTime(from.getCreateTime());
    }

    @Override
    public <E extends IDigitalIntelligenceResult> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public void forUpdate() {
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends DigitalIntelligenceResultEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.exam.jooq.tables.records.DigitalIntelligenceResultRecord r = new com.zxy.product.exam.jooq.tables.records.DigitalIntelligenceResultRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.ID) > -1){
                        r.setValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.ID, record.getValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.ID));
                    }
                    if(row.indexOf(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.NAME) > -1){
                        r.setValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.NAME, record.getValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.NAME));
                    }
                    if(row.indexOf(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.MEMBER_ID, record.getValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.ABILITY_TAGS) > -1){
                        r.setValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.ABILITY_TAGS, record.getValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.ABILITY_TAGS));
                    }
                    if(row.indexOf(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.STRENGTH_ENHANCEMENT) > -1){
                        r.setValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.STRENGTH_ENHANCEMENT, record.getValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.STRENGTH_ENHANCEMENT));
                    }
                    if(row.indexOf(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.SKILL_IMPROVEMENT) > -1){
                        r.setValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.SKILL_IMPROVEMENT, record.getValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.SKILL_IMPROVEMENT));
                    }
                    if(row.indexOf(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.SHORT_TERM_PLAN) > -1){
                        r.setValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.SHORT_TERM_PLAN, record.getValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.SHORT_TERM_PLAN));
                    }
                    if(row.indexOf(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.MEDIUM_TERM_PLAN) > -1){
                        r.setValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.MEDIUM_TERM_PLAN, record.getValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.MEDIUM_TERM_PLAN));
                    }
                    if(row.indexOf(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.RECOMMENDED_COURSES_ID) > -1){
                        r.setValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.RECOMMENDED_COURSES_ID, record.getValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.RECOMMENDED_COURSES_ID));
                    }
                    if(row.indexOf(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.IS_VIEW) > -1){
                        r.setValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.IS_VIEW, record.getValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.IS_VIEW));
                    }
                    if(row.indexOf(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.CREATE_TIME, record.getValue(com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.CREATE_TIME));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}

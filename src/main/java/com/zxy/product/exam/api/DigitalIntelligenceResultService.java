package com.zxy.product.exam.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.exam.entity.szfn.DigitalIntelligenceResult;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@RemoteService(timeout = 300000)
public interface DigitalIntelligenceResultService {

    @Transactional
    DigitalIntelligenceResult insert(DigitalIntelligenceResult digitalIntelligenceResult);

    @Transactional
    DigitalIntelligenceResult update(DigitalIntelligenceResult digitalIntelligenceResult);

    /**
     * 查看后修改状态
     * @param view
     * @param id
     */
    @Transactional
    void updateView(Integer view, String id);

    @Transactional
    String delete(String id);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<DigitalIntelligenceResult> get(String id);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<DigitalIntelligenceResult> findByMemberId(String memberId);

    /**
     * 查询个人测试信息
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<DigitalIntelligenceResult> getByMemberId(String memberId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<DigitalIntelligenceResult> findPagedResult(Integer page, Integer pageSize, Optional<String> name, Optional<String> memberId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<DigitalIntelligenceResult> findByIds(List<String> ids);

    @Transactional
    void batchInsert(List<DigitalIntelligenceResult> digitalIntelligenceResults);

    @Transactional
    void batchDelete(List<String> ids);

}
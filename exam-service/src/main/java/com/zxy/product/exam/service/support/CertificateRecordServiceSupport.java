package com.zxy.product.exam.service.support;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.exam.annotation.DataSource;
import com.zxy.product.exam.api.AnswerRecordService;
import com.zxy.product.exam.api.CertificateRecordService;
import com.zxy.product.exam.api.ExamNoticeService;
import com.zxy.product.exam.api.sequence.business.CertificateAuthCodeGenerator;
import com.zxy.product.exam.content.ErrorCode;
import com.zxy.product.exam.content.MessageConstant;
import com.zxy.product.exam.entity.*;
import com.zxy.product.exam.jooq.Tables;
import com.zxy.product.exam.jooq.tables.records.CertificateRecordRecord;
import com.zxy.product.exam.service.util.GetTableUtil;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.exam.jooq.Tables.*;
import static com.zxy.product.exam.jooq.tables.EquipmentType.EQUIPMENT_TYPE;
import static com.zxy.product.exam.jooq.tables.Exam.EXAM;
import static com.zxy.product.exam.jooq.tables.Member.MEMBER;
import static com.zxy.product.exam.jooq.tables.Organization.ORGANIZATION;
import static com.zxy.product.exam.jooq.tables.Profession.PROFESSION;

@Service
public class CertificateRecordServiceSupport implements CertificateRecordService{

    private CommonDao<CertificateRecord> certificateRecordDao;
    private CommonDao<ExamRegist> examRegistCommonDao;
    private CommonDao<Exam> examDao;
    private ExamNoticeService examNoticeService;
    private CertificateAuthCodeGenerator certificateCodeGenerator;

    private AnswerRecordService answerRecordService;
    private CommonDao<DeleteDataExam> deleteDataExamCommonDao;
    private CommonDao<Organization> organizationDao;
    private GetTableUtil getTableUtil;
    @Autowired
    public void setGetTableUtil(GetTableUtil getTableUtil) {
        this.getTableUtil = getTableUtil;
    }

    @Autowired
    public void setOrganizationDao(CommonDao<Organization> organizationDao) {
        this.organizationDao = organizationDao;
    }


    @Autowired
    public void setDeleteDataExamCommonDao(CommonDao<DeleteDataExam> deleteDataExamCommonDao) {
        this.deleteDataExamCommonDao = deleteDataExamCommonDao;
    }
    @Autowired
    public void setAnswerRecordService(AnswerRecordService answerRecordService) {
        this.answerRecordService = answerRecordService;
    }

    @Autowired
    public void setCertificateRecordDao(CommonDao<CertificateRecord> certificateRecordDao) {
        this.certificateRecordDao = certificateRecordDao;
    }

    @Autowired
    public void setExamDao(CommonDao<Exam> examDao) {
        this.examDao = examDao;
    }

    @Autowired
    public void setExamNoticeService(ExamNoticeService examNoticeService) {
        this.examNoticeService = examNoticeService;
    }

    @Autowired
    public void setCertificateCodeGenerator(CertificateAuthCodeGenerator certificateCodeGenerator) {
        this.certificateCodeGenerator = certificateCodeGenerator;
    }

    @Override
    public PagedResult<CertificateRecord> findPageResult(int page, int pageSize,
                                                         Optional<String> name, Optional<String> fullName, Optional<String> memberOrganizationId,
                                                         Optional<String> profession, Optional<String> subProfession,
                                                         Optional<String> professionLevel, Optional<String> equipmentType, Optional<Long> createTimeStart,
                                                         Optional<Long> createTimeEnd, Map<String, Set<String>> grantOrganizationPathMap) {
        return certificateRecordDao.execute(e -> {

            Field<String> organizationName = ORGANIZATION.NAME.as("organization_name");
            Field<String> organizationId = ORGANIZATION.ID.as("organization_id");
            Field<String> memberName = MEMBER.NAME.as("member_name");
            Field<String> memberFullName = MEMBER.FULL_NAME.as("member_full_name");
            Field<String> memberCompanyId = MEMBER.COMPANY_ID.as("member_company_id");
            com.zxy.product.exam.jooq.tables.Profession subProfessionTable = PROFESSION.as("sub_profession");

            SelectSelectStep<Record> selectListField = e.select(
                Fields.start()
                .add(CERTIFICATE_RECORD.ID)
                .add(CERTIFICATE_RECORD.MEMBER_ID)
                .add(CERTIFICATE_RECORD.CREATE_TIME)
                .add(CERTIFICATE_RECORD.REASON)
                .add(CERTIFICATE_RECORD.SCORE)
                .add(CERTIFICATE_RECORD.PASS_STATUS)
                .add(CERTIFICATE_RECORD.VALID_DATE)
                .add(CERTIFICATE_RECORD.NUM)
                .add(CERTIFICATE_RECORD.ISSUE_TIME)
                .add(PROFESSION.ID)
                .add(PROFESSION.NAME)
                .add(subProfessionTable.ID)
                .add(subProfessionTable.NAME)
                .add(PROFESSION_LEVEL.ID)
                .add(PROFESSION_LEVEL.LEVEL_NAME)
                .add(EQUIPMENT_TYPE.ID)
                .add(EQUIPMENT_TYPE.NAME)
                .add(organizationName)
                .add(organizationId)
                .add(memberName)
                .add(memberFullName)
                .add(memberCompanyId)
                .add(MEMBER.COMPANY_ID)
                .end()
            );

            List<Condition> conditions = Stream.of(
                name.map(MEMBER.NAME::contains),
                fullName.map(MEMBER.FULL_NAME::contains),
                profession.map(CERTIFICATE_RECORD.PROFESSION_ID::eq),
                subProfession.map(CERTIFICATE_RECORD.SUB_PROFESSION_ID::eq),
                professionLevel.map(CERTIFICATE_RECORD.PROFESSION_LEVEL_ID::eq),
                equipmentType.map(CERTIFICATE_RECORD.EQUIPMENT_TYPE_ID::eq),
                createTimeStart.map(CERTIFICATE_RECORD.CREATE_TIME::ge),
                createTimeEnd.map(CERTIFICATE_RECORD.CREATE_TIME::le),
                Optional.of(CERTIFICATE_RECORD.ACCESS_TYPE.eq(CertificateRecord.ACCESS_TYPE_IMPORT)),
                Optional.of(CERTIFICATE_RECORD.CLOUD.eq(CertificateRecord.IS_CLOUD_NO)),
//                Optional.of(MEMBER.COMPANY_ID.in(organizationIds))
                memberOrganizationId.map(id -> ORGANIZATION.PATH.startsWith(organizationDao.get(id).getPath()))
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());;

            generateOrganizationConditions(grantOrganizationPathMap,conditions);

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                SelectConditionStep<Record> select = a.from(CERTIFICATE_RECORD)
                    .leftJoin(MEMBER).on(CERTIFICATE_RECORD.MEMBER_ID.eq(MEMBER.ID))
                    .leftJoin(PROFESSION).on(PROFESSION.ID.eq(CERTIFICATE_RECORD.PROFESSION_ID))
                    .leftJoin(subProfessionTable).on(subProfessionTable.ID.eq(CERTIFICATE_RECORD.SUB_PROFESSION_ID))
                    .leftJoin(PROFESSION_LEVEL).on(PROFESSION_LEVEL.ID.eq(CERTIFICATE_RECORD.PROFESSION_LEVEL_ID))
                    .leftJoin(EQUIPMENT_TYPE).on(EQUIPMENT_TYPE.ID.eq(CERTIFICATE_RECORD.EQUIPMENT_TYPE_ID))
                    .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID)).where(conditions);
                return select;
            };


            SelectSelectStep<Record> selectCountField = e.select(
                Fields.start()
                .add(CERTIFICATE_RECORD.ID.countDistinct()).end()
            );

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> countStepFunc = a -> {
                SelectConditionStep<Record> select = a.from(CERTIFICATE_RECORD)
                    .leftJoin(MEMBER).on(CERTIFICATE_RECORD.MEMBER_ID.eq(MEMBER.ID))
                        .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                        .where(conditions);
                return select;
            };

//            int count = certificateRecordDao.count(conditions.toArray(new Condition[conditions.size()]));
            int count = countStepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            listSetp.orderBy(CERTIFICATE_RECORD.CREATE_TIME.desc());
            Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();

            return PagedResult.create(count, record.stream().map(t -> {
                CertificateRecord certificateRecord = t.into(CertificateRecord.class);
                // 用户
                Member member = new Member();
                member.setId(t.getValue(CERTIFICATE_RECORD.MEMBER_ID));
                member.setName(t.getValue(memberName));
                member.setFullName(t.getValue(memberFullName));
                member.setCompanyId(t.getValue(memberCompanyId));
                // 用户部门
                Organization organization = new Organization();
                organization.setId(t.getValue(organizationId));
                organization.setName(t.getValue(organizationName));
                member.setOrganization(organization);
                certificateRecord.setMember(member);
                // 专业
                Profession professions = t.into(PROFESSION).into(Profession.class);
                certificateRecord.setProfession(professions);
                // 子专业
                Profession subProfessions = t.into(subProfessionTable).into(Profession.class);
                certificateRecord.setSubProfession(subProfessions);
                // 等级
                ProfessionLevel professionLevels = t.into(ProfessionLevel.class);
                certificateRecord.setProfessionLevel(professionLevels);
                // 设备
                EquipmentType equipmentTypes = t.into(EquipmentType.class);
                certificateRecord.setEquipmentType(equipmentTypes);
                return certificateRecord;
            }).collect(Collectors.toList()));

        });
    }
    @Override
    public PagedResult<CertificateRecord> findCloudPageResult(int page, int pageSize,
            Optional<String> name, Optional<String> fullName, Optional<String> memberOrganizationId,
            Optional<String> profession, Optional<String> subProfession,
            Optional<String> professionLevel, Optional<String> equipmentType, Optional<Long> createTimeStart,
            Optional<Long> createTimeEnd, Map<String, Set<String>> grantOrganizationPathMap) {
        return certificateRecordDao.execute(e -> {

            Field<String> organizationName = ORGANIZATION.NAME.as("organization_name");
            Field<String> organizationId = ORGANIZATION.ID.as("organization_id");
            Field<String> memberName = MEMBER.NAME.as("member_name");
            Field<String> memberFullName = MEMBER.FULL_NAME.as("member_full_name");
            Field<String> memberCompanyId = MEMBER.COMPANY_ID.as("member_company_id");

            SelectSelectStep<Record> selectListField = e.select(
                    Fields.start()
                    .add(CERTIFICATE_RECORD.ID)
                    .add(CERTIFICATE_RECORD.MEMBER_ID)
                    .add(CERTIFICATE_RECORD.CREATE_TIME)
                    .add(CERTIFICATE_RECORD.REASON)
                    .add(CERTIFICATE_RECORD.SCORE)
                    .add(CERTIFICATE_RECORD.PASS_STATUS)
                    .add(CERTIFICATE_RECORD.VALID_DATE)
                    .add(CERTIFICATE_RECORD.NUM)
                    .add(CERTIFICATE_RECORD.ISSUE_TIME)
                    .add(CLOUD_PROFESSION.ID)
                    .add(CLOUD_PROFESSION.NAME)
                    .add(CLOUD_LEVEL.ID)
                    .add(CLOUD_LEVEL.LEVEL_NAME)
                    .add(organizationName)
                    .add(organizationId)
                    .add(memberName)
                    .add(memberFullName)
                    .add(memberCompanyId)
                    .add(MEMBER.COMPANY_ID)
                    .end()
                    );

            List<Condition> conditions = Stream.of(
                    name.map(MEMBER.NAME::contains),
                    fullName.map(MEMBER.FULL_NAME::contains),
                    profession.map(CERTIFICATE_RECORD.PROFESSION_ID::eq),
                    professionLevel.map(CERTIFICATE_RECORD.PROFESSION_LEVEL_ID::eq),
                    createTimeStart.map(CERTIFICATE_RECORD.CREATE_TIME::ge),
                    createTimeEnd.map(CERTIFICATE_RECORD.CREATE_TIME::le),
                    Optional.of(CERTIFICATE_RECORD.ACCESS_TYPE.eq(CertificateRecord.ACCESS_TYPE_IMPORT)),
                    Optional.of(CERTIFICATE_RECORD.CLOUD.eq(CertificateRecord.IS_CLOUD_YES)),
//                Optional.of(MEMBER.COMPANY_ID.in(organizationIds))
                    memberOrganizationId.map(id -> ORGANIZATION.PATH.startsWith(organizationDao.get(id).getPath()))
                    ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            generateOrganizationConditions(grantOrganizationPathMap,conditions);

                    Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                        SelectConditionStep<Record> select = a.from(CERTIFICATE_RECORD)
                                .leftJoin(MEMBER).on(CERTIFICATE_RECORD.MEMBER_ID.eq(MEMBER.ID))
                                .leftJoin(CLOUD_PROFESSION).on(CLOUD_PROFESSION.ID.eq(CERTIFICATE_RECORD.PROFESSION_ID))
                                .leftJoin(CLOUD_LEVEL).on(CLOUD_LEVEL.ID.eq(CERTIFICATE_RECORD.PROFESSION_LEVEL_ID))
                                .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID)).where(conditions);
                        return select;
                    };


                    SelectSelectStep<Record> selectCountField = e.select(
                            Fields.start()
                            .add(CERTIFICATE_RECORD.ID.countDistinct()).end()
                            );

                    Function<SelectSelectStep<Record>, SelectConditionStep<Record>> countStepFunc = a -> {
                        SelectConditionStep<Record> select = a.from(CERTIFICATE_RECORD)
                                .leftJoin(MEMBER).on(CERTIFICATE_RECORD.MEMBER_ID.eq(MEMBER.ID))
                                .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                                .where(conditions);
                        return select;
                    };

//            int count = certificateRecordDao.count(conditions.toArray(new Condition[conditions.size()]));
                    int count = countStepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

                    SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
                    listSetp.orderBy(CERTIFICATE_RECORD.CREATE_TIME.desc());
                    Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();

                    return PagedResult.create(count, record.stream().map(t -> {
                        CertificateRecord certificateRecord = t.into(CertificateRecord.class);
                        // 用户
                        Member member = new Member();
                        member.setId(t.getValue(CERTIFICATE_RECORD.MEMBER_ID));
                        member.setName(t.getValue(memberName));
                        member.setFullName(t.getValue(memberFullName));
                        member.setCompanyId(t.getValue(memberCompanyId));
                        // 用户部门
                        Organization organization = new Organization();
                        organization.setId(t.getValue(organizationId));
                        organization.setName(t.getValue(organizationName));
                        member.setOrganization(organization);
                        certificateRecord.setMember(member);
                        // 专业
                        Profession professions = new Profession();
                        professions.setId(t.getValue(CLOUD_PROFESSION.ID));
                        professions.setName(t.getValue(CLOUD_PROFESSION.NAME));
                        certificateRecord.setProfession(professions);
                        // 等级
                        ProfessionLevel professionLevels = new ProfessionLevel();
                        professionLevels.setId(t.getValue(CLOUD_LEVEL.ID));
                        professionLevels.setLevelName(t.getValue(CLOUD_LEVEL.LEVEL_NAME));
                        certificateRecord.setProfessionLevel(professionLevels);
                        return certificateRecord;
                    }).collect(Collectors.toList()));

        });
    }

    /**
     * 拼装组织条件
     *
     * @param grantOrganizationMap 组织Map
     * @param conditions
     * return 组织条件
     */
    private void generateOrganizationConditions(Map<String, Set<String>> grantOrganizationMap, List<Condition> conditions) {
        Set<String> organizationIdSet = grantOrganizationMap.get(com.zxy.product.system.entity.Organization.NOT_INCLUDE_KEY);
        Set<String> pathSet = grantOrganizationMap.get(com.zxy.product.system.entity.Organization.INCLUDE_KEY);
        if (!CollectionUtils.isEmpty(pathSet) || !CollectionUtils.isEmpty(organizationIdSet)) {
            Condition condition;
            if (pathSet.isEmpty()) {
                condition = Optional.of(organizationIdSet).map(Tables.ORGANIZATION.ID::in).orElse(DSL.trueCondition());
            } else {
                condition = pathSet.stream().map(Tables.ORGANIZATION.PATH::startsWith).reduce(DSL::or)
                        .orElse(DSL.trueCondition());
                if (!organizationIdSet.isEmpty()) {
                    condition = condition.or(Tables.ORGANIZATION.ID.in(organizationIdSet));
                }
            }
            conditions.add(condition);
        }
    }

    @Override
    public List<CertificateRecord> findMyGridList(String currentUserId) {
        return certificateRecordDao.execute(ar -> {
            return ar.select(
                    Fields.start()
                            .add(CERTIFICATE_RECORD.ID)
                            .add(CERTIFICATE_RECORD.PROFESSION_LEVEL_ID)
                            .end())
                    .from(CERTIFICATE_RECORD)
                    .where(CERTIFICATE_RECORD.MEMBER_ID.eq(currentUserId))
                    .and(CERTIFICATE_RECORD.GRID.eq(CertificateRecord.IS_GRID_YES))
                    .and(CERTIFICATE_RECORD.IS_CURRENT.eq(CertificateRecord.IS_CURRENT_YES))
                    .groupBy(CERTIFICATE_RECORD.PROFESSION_LEVEL_ID)
                    .fetch().stream().map(r ->{
                        CertificateRecord certificateRecord = new CertificateRecord();
                        certificateRecord.setId(r.getValue(CERTIFICATE_RECORD.ID));
                        certificateRecord.setProfessionLevelId(r.getValue(CERTIFICATE_RECORD.PROFESSION_LEVEL_ID));
                        return certificateRecord;
                    }).collect(Collectors.toList());
        });
    }

    @Override
    public List<CertificateRecord> findByMemberIds(List<String> ids) {
        return certificateRecordDao.fetch(CERTIFICATE_RECORD.MEMBER_ID.in(ids).and(CERTIFICATE_RECORD.IS_CURRENT.eq(CertificateRecord.IS_CURRENT_YES)));
    }

    @Override
    public List<CertificateRecord> batchInsert(List<CertificateRecord> list) {
        certificateRecordDao.insert(list);
        return list;
    }

    @Override
    public List<CertificateRecord> batchUpdateForImport(List<CertificateRecord> list) {
        List<UpdateConditionStep<CertificateRecordRecord>> updates = new ArrayList<>();
        list.forEach(cr -> updates.add(
                DSL.update(CERTIFICATE_RECORD)
                        .set(CERTIFICATE_RECORD.SCORE, cr.getScore())
                        .set(CERTIFICATE_RECORD.SCORE_LEVEL, cr.getScoreLevel())
                        .set(CERTIFICATE_RECORD.CREATE_TIME, cr.getCreateTime())
                        .set(CERTIFICATE_RECORD.ISSUE_TIME, cr.getIssueTime())
                        .set(CERTIFICATE_RECORD.VALID_DATE, cr.getValidDate())
                        .set(CERTIFICATE_RECORD.REASON, cr.getReason())
                        .where(CERTIFICATE_RECORD.ID.eq(cr.getId()))
            )
        );
        certificateRecordDao.execute(e -> e.batch(updates).execute());
        return list;
    }


    /**
     * 查询考试管理里面的证书列表
     * @param page
     * @param pageSize
     * @param fullName
     * @param name
     * @param organizationId
     * @param examState
     * @param issueState
     * @return
     */
    @Override
    @DataSource
    public PagedResult<ExamRegist> findExamCertResult(Integer page, Integer pageSize, Optional<String> fullName, Optional<String> name,
                                                      Optional<String> organizationId, Optional<Integer> examState, Optional<Integer> issueState,String examId) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        return examRegistCommonDao.execute(e -> {

            Field<String> memberName = MEMBER.NAME.as("member_name");
            Field<String> memberFullName = MEMBER.FULL_NAME.as("member_full_name");
            Field<String> organizationNameField = ORGANIZATION.NAME.as("organization_name");
            Field<String> organizationIdField = ORGANIZATION.ID.as("organization_id");

            SelectSelectStep<Record> selectListField = e.select(
                    Fields.start()
                            .add(memberName)
                            .add(memberFullName)
                            .add(organizationNameField)
                            .add(organizationIdField)
                            .add(EXAM.START_TIME)
                            .add(examRegistTable.field("f_top_score", Integer.class))
                            .add(examRegistTable.field("f_exam_id", String.class))
                            .add(examRegistTable.field("f_id", String.class))
                            .add(examRegistTable.field("f_pass_status", Integer.class))
                            .add(examRegistTable.field("f_top_score", Integer.class))
                            .add(examRegistTable.field("f_certificate_issue", Integer.class))
                            .add(examRecordTable.field("f_start_time", Long.class))
                            .add(EXAM.NAME)
                            .end()
            );

            SelectSelectStep<Record> selectCountField = e.select(Fields.start().add(examRegistTable.field("f_id", String.class).count()).end());

            List<Condition> conditions = Stream.of(
                    fullName.map(MEMBER.FULL_NAME::contains),
                    name.map(MEMBER.NAME::contains),
                    organizationId.map(ORGANIZATION.ID::eq),
                    issueState.map(is -> {
                        if (is == ExamRegist.CERTIFICATE_ISSUE_YES) {
                            return examRegistTable.field("f_certificate_issue", Integer.class).eq(is);
                        }
                        if (is == ExamRegist.CERTIFICATE_ISSUE_NO) {
                            return examRegistTable.field("f_certificate_issue", Integer.class).eq(is).or(examRegistTable.field("f_certificate_issue", Integer.class).isNull());
                        }
                        return DSL.trueCondition();
                    }),
                    examState.map(examRegistTable.field("f_pass_status", Integer.class)::eq)
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

			Condition notRefuseCond = examRegistTable.field("f_top_score_record_id", String.class).isNull().and(examRegistTable.field("f_status", Integer.class).ne(ExamRegist.STATUS_BE_REFUSE));
			conditions.add(examRegistTable.field("f_top_score_record_id", String.class).isNotNull().or(notRefuseCond));
			conditions.add(examRegistTable.field("f_exam_id", String.class).eq(examId));

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                SelectConditionStep<Record> select  = a.from(examRegistTable)
                                .leftJoin(EXAM).on(examRegistTable.field("f_exam_id", String.class).eq(EXAM.ID))
                                .leftJoin(examRecordTable).on(examRecordTable.field("f_id", String.class).eq(examRegistTable.field("f_top_score_record_id", String.class)))
                                .leftJoin(MEMBER).on(examRegistTable.field("f_member_id", String.class).eq(MEMBER.ID))
                                .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                        .where(conditions);

                return select;
            };

            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0,Integer.class);


            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
//            listSetp.orderBy(EXAM.START_TIME.desc());
            Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();

            return PagedResult.create(count, record.stream().map(t -> {

                ExamRegist examRegist = new ExamRegist();
                examRegist.setTopScore(t.getValue(examRegistTable.field("f_top_score", Integer.class)));
                examRegist.setExamId(t.getValue(examRegistTable.field("f_exam_id", String.class)));
                examRegist.setId(t.getValue(examRegistTable.field("f_id", String.class)));
                examRegist.setPassStatus(t.getValue(examRegistTable.field("f_pass_status", Integer.class)));
                examRegist.setTopScore(t.getValue(examRegistTable.field("f_top_score", Integer.class)));
                examRegist.setCertificateIssue(t.getValue(examRegistTable.field("f_certificate_issue", Integer.class)));

                if (examRegist.getCertificateIssue() == null){
                    examRegist.setCertificateIssue(ExamRegist.CERTIFICATE_ISSUE_NO);
                }
                Member member = new Member();
                member.setName(t.getValue(memberName));
                member.setFullName(t.getValue(memberFullName));
                examRegist.setMember(member);
                Organization organization = new Organization();
                organization.setName(t.getValue(organizationNameField));
                organization.setId(t.getValue(organizationIdField));
                examRegist.setOrganization(organization);
                examRegist.setStartTime(t.getValue(examRecordTable.field("f_start_time", Long.class)));
                examRegist.setExamName(t.getValue(EXAM.NAME));
                return examRegist;
            }).collect(Collectors.toList()));
        });
    }

    @Override
    public List<CertificateRecord> issue(List<String> registIds, String examId, String currentMemberId,
            Optional<Integer> noticeUser, Optional<String> noticeUserText, Optional<String> noticeUserContent) {

        TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));

        // 定义一个集合用来存放本次发放的所有的证书编号
        List<String> numList = new ArrayList<String>();
        Exam exam = examDao.get(examId);
        // 是否到了证书发放时间
        String professionId = exam.getProfessionId();
        String subProfessionId = exam.getSubProfessionId();
        String levelId = exam.getLevelId();
        String equipmentTypeId = exam.getEquipmentTypeId();
        String certificateId = exam.getCertificateId();
        String evalutionRuleJson = exam.getEvalutionRule();
        Map<String, Object> evalutionRuleMap = (Map<String, Object>)JSON.parse(evalutionRuleJson);

        List<ExamRegist> examRegists = examRegistCommonDao.execute(ar -> {
            return ar.select(
                    Fields.start()
                    .add(examRegistTable.field("f_id", String.class))
                    .add(examRegistTable.field("f_create_time", Long.class))
                    .add(examRegistTable.field("f_exam_id", String.class))
                    .add(examRegistTable.field("f_member_id", String.class))
                    .add(examRegistTable.field("f_status", Integer.class))
                    .add(examRegistTable.field("f_top_score", Integer.class))
                    .add(examRegistTable.field("f_type", Integer.class))
                    .add(examRegistTable.field("f_exam_times", Integer.class))
                    .add(examRegistTable.field("f_top_score_record_id", String.class))
                    .add(examRegistTable.field("f_certificate_issue", Integer.class))
                    .add(examRegistTable.field("f_pass_status", Integer.class))
                    .end())
                    .from(examRegistTable)
                    .where(examRegistTable.field("f_id", String.class).in(registIds))
                    .fetch().stream().map(r ->{
                        ExamRegist examRegist = new ExamRegist();
                        examRegist.setId(r.getValue(examRegistTable.field("f_id", String.class)));
                        examRegist.setCreateTime(r.getValue(examRegistTable.field("f_create_time", Long.class)));
                        examRegist.setExamId(r.getValue(examRegistTable.field("f_exam_id", String.class)));
                        examRegist.setMemberId(r.getValue(examRegistTable.field("f_member_id", String.class)));
                        examRegist.setStatus(r.getValue(examRegistTable.field("f_status", Integer.class)));
                        examRegist.setTopScore(r.getValue(examRegistTable.field("f_top_score", Integer.class)));
                        examRegist.setType(r.getValue(examRegistTable.field("f_type", Integer.class)));
                        examRegist.setExamTimes(r.getValue(examRegistTable.field("f_exam_times", Integer.class)));
                        examRegist.setTopScoreRecordId(r.getValue(examRegistTable.field("f_top_score_record_id", String.class)));
                        examRegist.setCertificateIssue(r.getValue(examRegistTable.field("f_certificate_issue", Integer.class)));
                        examRegist.setPassStatus(r.getValue(examRegistTable.field("f_pass_status", Integer.class)));
                        return examRegist;
                    }).collect(Collectors.toList());
        });


        List<String> memberIds = examRegists.stream().map(er -> er.getMemberId()).collect(Collectors.toList());
        List<CertificateRecord> certificateRecords = certificateRecordDao.fetch(
                CERTIFICATE_RECORD.MEMBER_ID.in(memberIds)
                .and(CERTIFICATE_RECORD.PROFESSION_ID.eq(professionId))
                .and(CERTIFICATE_RECORD.SUB_PROFESSION_ID.eq(subProfessionId))
                .and(CERTIFICATE_RECORD.PROFESSION_LEVEL_ID.eq(levelId))
                .and(CERTIFICATE_RECORD.EQUIPMENT_TYPE_ID.eq(equipmentTypeId))
                );
        ProfessionLevel professionLevel = exam.getLevel();
        Profession profession = exam.getProfession();
        Profession subProfession = exam.getProfession();
        EquipmentType equipmentType = exam.getEquipmentType();
        // 发放证书（学员可以有多个（同专业、子专业、等级、设备型号）证书，同专业、子专业、等级、设备型号的证书有效期以最后发放的为准。）
        int year = LocalDate.now().getYear();
        List<CertificateRecord> records = examRegists.stream().map(eg -> {
            CertificateRecord record = new CertificateRecord();
            record.forInsert();
            record.setExamId(examId);
            record.setMemberId(record.getMemberId());
            record.setProfessionId(professionId);
            record.setSubProfessionId(subProfessionId);
            record.setProfessionLevelId(levelId);
            record.setEquipmentTypeId(equipmentTypeId);
            // 证书编号规则
            String code = null;
            try {
                code = certificateCodeGenerator.getCode(Optional.ofNullable(professionLevel.getLevel()),Optional.ofNullable(profession.getCode()),Optional.ofNullable(subProfession.getCode()),
                        Optional.ofNullable(equipmentType.getCode()),"010", String.valueOf(year), numList);
            } catch (Exception e) {
                e.printStackTrace();
                throw new UnprocessableException(ErrorCode.CertificateCodeGeneratError); // 证书编号生成失败
            }
            record.setNum(code);
            record.setAccessType(CertificateRecord.ACCESS_TYPE_EXAM);
            record.setPassStatus(CertificateRecord.PASS_STATUS_PASS);
            // 成绩级别，0不及格 1及格 2良好 3优秀
            Integer score = eg.getTopScore();
            Integer scoreLevel = CertificateRecord.SCORE_LEVEL_NOT_PASS;
            Integer excellentLess = evalutionRuleMap.get("excellentLess") == null ? null : Integer.valueOf(String.valueOf(evalutionRuleMap.get("excellentLess")));
            Integer goodLess = evalutionRuleMap.get("goodLess") == null ? null : Integer.valueOf(String.valueOf(evalutionRuleMap.get("goodLess")));
            Integer passLess = evalutionRuleMap.get("passLess") == null ? null : Integer.valueOf(String.valueOf(evalutionRuleMap.get("passLess")));
            if(excellentLess != null && score >= excellentLess * 100) { // 优秀
                scoreLevel = CertificateRecord.SCORE_LEVEL_EXCELLENT;
            } else if ( excellentLess != null && goodLess != null && score < excellentLess * 100 && score >= goodLess * 100) { // 良好
                scoreLevel = CertificateRecord.SCORE_LEVEL_GOOD;
            } else if ( goodLess != null && passLess != null && score < goodLess * 100 && score >= passLess * 100) { // 及格
                scoreLevel = CertificateRecord.SCORE_LEVEL_PASS;
            }
            record.setScore(score);
            record.setScoreLevel(scoreLevel);
            record.setTemplateId(certificateId);
            Long time = record.getCreateTime();
            record.setIssueTime(time);
            record.setValidDate(professionLevel.getValidDate() == null || professionLevel.getValidDate() == 0 ? null : LocalDate.ofEpochDay(time).plusYears(professionLevel.getValidDate()).toEpochDay());
            record.setIsCurrent(CertificateRecord.IS_CURRENT_YES);
            return record;
        }).collect(Collectors.toList());
        List<String> recordIds = certificateRecords.stream().map(cr -> cr.getId()).collect(Collectors.toList());
        certificateRecordDao.insert(records);
        certificateRecordDao.execute(e -> {// 原来的证书置为非最新
            return e.update(CERTIFICATE_RECORD)
                    .set(CERTIFICATE_RECORD.IS_CURRENT, CertificateRecord.IS_CURRENT_NO)
                    .where(CERTIFICATE_RECORD.ID.in(recordIds))
                    .execute();
        });

        // 消息通知
        examNoticeService.insert(ExamNotice.BUSINESS_TYPE_EXAM, examId, ExamNotice.TYPE_CERTIFICATE_ISSUE,
            currentMemberId, MessageConstant.EXAM_CERTIFICATE_ISSUE, Optional.of(Exam.EXAM_YES),
            noticeUserText, noticeUserContent, Optional.ofNullable(memberIds.stream().collect(Collectors.joining(","))));
        return records;
    }

    @Autowired
    public void setExamRegistCommonDao(CommonDao<ExamRegist> examRegistCommonDao) {
        this.examRegistCommonDao = examRegistCommonDao;
    }

    @Override
    public List<CertificateRecord> findList(Optional<String> memberId,
            Optional<String> examId) {
        return certificateRecordDao.execute(e -> {

            com.zxy.product.exam.jooq.tables.Profession subProfessionTable = PROFESSION.as("sub_profession");

            SelectSelectStep<Record> selectListField = e.select(
                Fields.start()
                .add(CERTIFICATE_RECORD.ID)
                .add(CERTIFICATE_RECORD.MEMBER_ID)
                .add(CERTIFICATE_RECORD.CREATE_TIME)
                .add(CERTIFICATE_RECORD.REASON)
                .add(CERTIFICATE_RECORD.SCORE)
                .add(CERTIFICATE_RECORD.PASS_STATUS)
                .add(CERTIFICATE_RECORD.VALID_DATE)
                .add(CERTIFICATE_RECORD.NUM)
                .add(CERTIFICATE_RECORD.ISSUE_TIME)
                .add(CERTIFICATE_RECORD.NAME)
                .add(PROFESSION.ID)
                .add(PROFESSION.NAME)
                .add(subProfessionTable.ID)
                .add(subProfessionTable.NAME)
                .add(PROFESSION_LEVEL.ID)
                .add(PROFESSION_LEVEL.LEVEL_NAME)
                .add(EQUIPMENT_TYPE.ID)
                .add(EQUIPMENT_TYPE.NAME)
                .end()
            );

            List<Condition> conditions = Stream.of(
                memberId.map(CERTIFICATE_RECORD.MEMBER_ID::eq),
                examId.map(CERTIFICATE_RECORD.EXAM_ID::eq),
                Optional.of(CERTIFICATE_RECORD.ACCESS_TYPE.eq(CertificateRecord.ACCESS_TYPE_IMPORT)
                        .or(EXAM.TYPE.eq(Exam.EXAM_AUTHENTICATION_TYPE)).or(EXAM.TYPE.eq(Exam.EXAM_AUTHENTICATION_PROVINCE_TYPE))
                        .or(ARCHIVED_EXAM.TYPE.eq(Exam.EXAM_AUTHENTICATION_TYPE)).or(ARCHIVED_EXAM.TYPE.eq(Exam.EXAM_AUTHENTICATION_PROVINCE_TYPE))),
                Optional.of(CERTIFICATE_RECORD.CLOUD.eq(CertificateRecord.IS_CLOUD_NO))
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());;

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                SelectConditionStep<Record> select = a.from(CERTIFICATE_RECORD)
                    .leftJoin(EXAM).on(EXAM.ID.eq(CERTIFICATE_RECORD.EXAM_ID))
                    .leftJoin(ARCHIVED_EXAM).on(ARCHIVED_EXAM.ID.eq(CERTIFICATE_RECORD.EXAM_ID))
                    .leftJoin(PROFESSION).on(PROFESSION.ID.eq(CERTIFICATE_RECORD.PROFESSION_ID))
                    .leftJoin(subProfessionTable).on(subProfessionTable.ID.eq(CERTIFICATE_RECORD.SUB_PROFESSION_ID))
                    .leftJoin(PROFESSION_LEVEL).on(PROFESSION_LEVEL.ID.eq(CERTIFICATE_RECORD.PROFESSION_LEVEL_ID))
                    .leftJoin(EQUIPMENT_TYPE).on(EQUIPMENT_TYPE.ID.eq(CERTIFICATE_RECORD.EQUIPMENT_TYPE_ID))
                    .where(conditions);
                return select;
            };


            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            listSetp.orderBy(CERTIFICATE_RECORD.CREATE_TIME.desc());
            Result<Record> record = listSetp.fetch();

            return record.stream().map(t -> {
                CertificateRecord certificateRecord = t.into(CertificateRecord.class);
                // 专业
                Profession professions = t.into(PROFESSION).into(Profession.class);
                certificateRecord.setProfession(professions);
                // 子专业
                Profession subProfessions = t.into(subProfessionTable).into(Profession.class);
                certificateRecord.setSubProfession(subProfessions);
                // 等级
                ProfessionLevel professionLevels = t.into(PROFESSION_LEVEL).into(ProfessionLevel.class);
                certificateRecord.setProfessionLevel(professionLevels);
                // 设备
                EquipmentType equipmentTypes = t.into(EQUIPMENT_TYPE).into(EquipmentType.class);
                certificateRecord.setEquipmentType(equipmentTypes);
                return certificateRecord;
            }).collect(Collectors.toList());

        });
    }

    @Override
    public List<CertificateRecord> findCloudList(Optional<String> memberId,
            Optional<String> examId) {
        return certificateRecordDao.execute(e -> {

            SelectSelectStep<Record> selectListField = e.select(
                    Fields.start()
                    .add(CERTIFICATE_RECORD.ID)
                    .add(CERTIFICATE_RECORD.MEMBER_ID)
                    .add(CERTIFICATE_RECORD.CREATE_TIME)
                    .add(CERTIFICATE_RECORD.REASON)
                    .add(CERTIFICATE_RECORD.SCORE)
                    .add(CERTIFICATE_RECORD.PASS_STATUS)
                    .add(CERTIFICATE_RECORD.VALID_DATE)
                    .add(CERTIFICATE_RECORD.NUM)
                    .add(CERTIFICATE_RECORD.ISSUE_TIME)
                    .add(CERTIFICATE_RECORD.NAME)
                    .add(CLOUD_PROFESSION.ID)
                    .add(CLOUD_PROFESSION.NAME)
                    .add(CLOUD_LEVEL.ID)
                    .add(CLOUD_LEVEL.LEVEL_NAME)
                    .end()
                    );

            List<Condition> conditions = Stream.of(
                    memberId.map(CERTIFICATE_RECORD.MEMBER_ID::eq),
                    examId.map(CERTIFICATE_RECORD.EXAM_ID::eq),
                    Optional.of(CERTIFICATE_RECORD.CLOUD.eq(CertificateRecord.IS_CLOUD_YES))
                    ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());;

                    Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                        SelectConditionStep<Record> select = a.from(CERTIFICATE_RECORD)
                                .leftJoin(EXAM).on(EXAM.ID.eq(CERTIFICATE_RECORD.EXAM_ID))
                                .leftJoin(CLOUD_PROFESSION).on(CLOUD_PROFESSION.ID.eq(CERTIFICATE_RECORD.PROFESSION_ID))
                                .leftJoin(CLOUD_LEVEL).on(CLOUD_LEVEL.ID.eq(CERTIFICATE_RECORD.PROFESSION_LEVEL_ID))
                                .where(conditions);
                        return select;
                    };


                    SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
                    listSetp.orderBy(CERTIFICATE_RECORD.CREATE_TIME.desc());
                    Result<Record> record = listSetp.fetch();

                    return record.stream().map(t -> {

                        CertificateRecord certificateRecord = new CertificateRecord();
                        certificateRecord.setId(t.getValue(CERTIFICATE_RECORD.ID));
                        certificateRecord.setMemberId(t.getValue(CERTIFICATE_RECORD.MEMBER_ID));
                        certificateRecord.setCreateTime(t.getValue(CERTIFICATE_RECORD.CREATE_TIME));
                        certificateRecord.setReason(t.getValue(CERTIFICATE_RECORD.REASON));
                        certificateRecord.setScore(t.getValue(CERTIFICATE_RECORD.SCORE));
                        certificateRecord.setPassStatus(t.getValue(CERTIFICATE_RECORD.PASS_STATUS));
                        certificateRecord.setValidDate(t.getValue(CERTIFICATE_RECORD.VALID_DATE));
                        certificateRecord.setNum(t.getValue(CERTIFICATE_RECORD.NUM));
                        certificateRecord.setIssueTime(t.getValue(CERTIFICATE_RECORD.ISSUE_TIME));
                        certificateRecord.setName(t.getValue(CERTIFICATE_RECORD.NAME));
                        // 专业
                        CloudProfession cloudProfession = new CloudProfession();
                        cloudProfession.setId(t.getValue(CLOUD_PROFESSION.ID));
                        cloudProfession.setName(t.getValue(CLOUD_PROFESSION.NAME));
                        certificateRecord.setCloudProfession(cloudProfession);
                        // 等级
                        CloudLevel cloudLevel = new CloudLevel();
                        cloudLevel.setId(t.getValue(CLOUD_LEVEL.ID));
                        cloudLevel.setLevelName(t.getValue(CLOUD_LEVEL.LEVEL_NAME));
                        certificateRecord.setCloudLevel(cloudLevel);

                        return certificateRecord;
                    }).collect(Collectors.toList());

        });
    }

    @Override
    public List<CertificateRecord> findGridList(Optional<String> memberId,
            Optional<String> examId) {
        return certificateRecordDao.execute(e -> {

            SelectSelectStep<Record> selectListField = e.select(
                    Fields.start()
                    .add(CERTIFICATE_RECORD.ID)
                    .add(CERTIFICATE_RECORD.MEMBER_ID)
                    .add(CERTIFICATE_RECORD.CREATE_TIME)
                    .add(CERTIFICATE_RECORD.REASON)
                    .add(CERTIFICATE_RECORD.SCORE)
                    .add(CERTIFICATE_RECORD.PASS_STATUS)
                    .add(CERTIFICATE_RECORD.VALID_DATE)
                    .add(CERTIFICATE_RECORD.NUM)
                    .add(CERTIFICATE_RECORD.ISSUE_TIME)
                    .add(CERTIFICATE_RECORD.NAME)
                    .add(GRID_LEVEL.ID)
                    .add(GRID_LEVEL.LEVEL_NAME)
                    .end()
                    );

            List<Condition> conditions = Stream.of(
                    memberId.map(CERTIFICATE_RECORD.MEMBER_ID::eq),
                    examId.map(CERTIFICATE_RECORD.EXAM_ID::eq),
                    Optional.of(CERTIFICATE_RECORD.GRID.eq(CertificateRecord.IS_GRID_YES))
                    ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());;

                    Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {

                        SelectConditionStep<Record> select = a.from(CERTIFICATE_RECORD)
                                .leftJoin(EXAM).on(EXAM.ID.eq(CERTIFICATE_RECORD.EXAM_ID))
                                .leftJoin(GRID_LEVEL).on(GRID_LEVEL.ID.eq(CERTIFICATE_RECORD.PROFESSION_LEVEL_ID))
                                .where(conditions);
                        return select;
                    };

                    SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
                    listSetp.orderBy(CERTIFICATE_RECORD.CREATE_TIME.desc());
                    Result<Record> record = listSetp.fetch();

                    return record.stream().map(t -> {

                        CertificateRecord certificateRecord = new CertificateRecord();
                        certificateRecord.setId(t.getValue(CERTIFICATE_RECORD.ID));
                        certificateRecord.setMemberId(t.getValue(CERTIFICATE_RECORD.MEMBER_ID));
                        certificateRecord.setCreateTime(t.getValue(CERTIFICATE_RECORD.CREATE_TIME));
                        certificateRecord.setReason(t.getValue(CERTIFICATE_RECORD.REASON));
                        certificateRecord.setScore(t.getValue(CERTIFICATE_RECORD.SCORE));
                        certificateRecord.setPassStatus(t.getValue(CERTIFICATE_RECORD.PASS_STATUS));
                        certificateRecord.setValidDate(t.getValue(CERTIFICATE_RECORD.VALID_DATE));
                        certificateRecord.setNum(t.getValue(CERTIFICATE_RECORD.NUM));
                        certificateRecord.setIssueTime(t.getValue(CERTIFICATE_RECORD.ISSUE_TIME));
                        certificateRecord.setName(t.getValue(CERTIFICATE_RECORD.NAME));

                        // 等级
                        GridLevel gridLevel = new GridLevel();
                        gridLevel.setId(t.getValue(GRID_LEVEL.ID));
                        gridLevel.setLevelName(t.getValue(GRID_LEVEL.LEVEL_NAME));
                        certificateRecord.setGridLevel(gridLevel);

                        return certificateRecord;
                    }).collect(Collectors.toList());

        });
    }

    @Override
    public String delete(String id) {
        Optional<CertificateRecord> record = certificateRecordDao.getOptional(id);
        record.ifPresent(r -> {
            certificateRecordDao.delete(id);
            deleteDataExamCommonDao.insert(DeleteDataExam.getDeleteData(DeleteDataExam.CERTIFICATE_RECORD,id,""));
            // 如果被删除的这个证书是最新的证书，那么要将这个用户最新的证书
//            if (CertificateRecord.IS_CURRENT_YES.equals(r.getIsCurrent())) {
//
//            }
        });
        return id;
    }

    @Override
    public Integer findExamCertificateRecordCount() {
        return certificateRecordDao.execute(e -> e.select(DSL.count(CERTIFICATE_RECORD.ID))
                .from(CERTIFICATE_RECORD)
                .fetchOne(DSL.count(CERTIFICATE_RECORD.ID)));
    }

    @Override
    public List<CertificateRecord> findExamCertificateRecord(int page, Integer pageSize) {
        return certificateRecordDao.execute(x -> {

            com.zxy.product.exam.jooq.tables.Profession professionTable = PROFESSION.as("profession");
            com.zxy.product.exam.jooq.tables.Profession subProfessionTable = PROFESSION.as("sub_profession");
            // 专业名称
            Field<String> professionName = professionTable.NAME.as("profession_name");
            // 子专业名称
            Field<String> subProfessionName = subProfessionTable.NAME.as("sub_profession_name");

            return x.select(Fields.start()

                    .add(CERTIFICATE_RECORD.ID)
                    .add(MEMBER.NAME)
                    .add(MEMBER.FULL_NAME)
                    .add(professionName)
                    .add(subProfessionName)
                    .add(EQUIPMENT_TYPE.NAME)
                    .add(PROFESSION_LEVEL.LEVEL_NAME)
                    .add(CERTIFICATE_RECORD.SCORE_LEVEL)
                    .add(CERTIFICATE_RECORD.ISSUE_TIME)
                    .add(CERTIFICATE_RECORD.VALID_DATE)
                    .end())
                    .from(CERTIFICATE_RECORD)
                    .leftJoin(MEMBER).on(MEMBER.ID.eq(CERTIFICATE_RECORD.MEMBER_ID))
                    .leftJoin(professionTable).on(professionTable.ID.eq(CERTIFICATE_RECORD.PROFESSION_ID))
                    .leftJoin(subProfessionTable).on(subProfessionTable.ID.eq(CERTIFICATE_RECORD.SUB_PROFESSION_ID))
                    .leftJoin(EQUIPMENT_TYPE).on(EQUIPMENT_TYPE.ID.eq(CERTIFICATE_RECORD.EQUIPMENT_TYPE_ID))
                    .leftJoin(PROFESSION_LEVEL).on(PROFESSION_LEVEL.ID.eq(CERTIFICATE_RECORD.PROFESSION_LEVEL_ID))
                    .limit((page - 1) * pageSize, pageSize)
                    .fetch().stream().map(r -> {
                        CertificateRecord certificateRecord = new CertificateRecord();
                        certificateRecord.setId(r.getValue(CERTIFICATE_RECORD.ID));
                        certificateRecord.setScoreLevel(r.getValue(CERTIFICATE_RECORD.SCORE_LEVEL));
                        certificateRecord.setIssueTime(r.getValue(CERTIFICATE_RECORD.ISSUE_TIME));
                        certificateRecord.setValidDate(r.getValue(CERTIFICATE_RECORD.VALID_DATE));
                        // 用户
                        Member member = new Member();
                        member.setName(r.getValue(MEMBER.NAME)!=null?r.getValue(MEMBER.NAME).trim():r.getValue(MEMBER.NAME));
                        member.setFullName(r.getValue(MEMBER.FULL_NAME)!=null?r.getValue(MEMBER.FULL_NAME).trim():r.getValue(MEMBER.FULL_NAME));
                        certificateRecord.setMember(member);
                        // 专业
                        Profession profession = new Profession();
                        profession.setName(r.getValue(professionName));
                        certificateRecord.setProfession(profession);
                        // 子专业
                        Profession subProfession = new Profession();
                        subProfession.setName(r.getValue(subProfessionName));
                        certificateRecord.setSubProfession(subProfession);
                        // 设备型号
                        EquipmentType equipmentType = new EquipmentType();
                        equipmentType.setName(r.getValue(EQUIPMENT_TYPE.NAME));
                        certificateRecord.setEquipmentType(equipmentType);
                        // 级别
                        ProfessionLevel professionLevel = new ProfessionLevel();
                        professionLevel.setLevelName(r.getValue(PROFESSION_LEVEL.LEVEL_NAME));
                        certificateRecord.setProfessionLevel(professionLevel);

                        return certificateRecord;

                    }).collect(Collectors.toList());

            });
      }


    @Override
    public List<CertificateRecord> findExamCertificateRecord(int page, Integer pageSize, Optional<Long> startTime, Optional<Long> endTime) {
        List<Condition> conditions = new ArrayList<>();
        if(startTime.isPresent() && endTime.isPresent()){
            conditions.add(CERTIFICATE_RECORD.MODIFY_DATE.between(new Timestamp(startTime.get()), new Timestamp(endTime.get())));
        }
        return certificateRecordDao.execute(x -> {
            return x.select(Fields.start()
                            .add(CERTIFICATE_RECORD.ID,CERTIFICATE_RECORD.EXAM_ID,CERTIFICATE_RECORD.MEMBER_ID,CERTIFICATE_RECORD.NUM,
                                    CERTIFICATE_RECORD.PROFESSION_ID,CERTIFICATE_RECORD.SUB_PROFESSION_ID,CERTIFICATE_RECORD.EQUIPMENT_TYPE_ID,
                                    CERTIFICATE_RECORD.PROFESSION_LEVEL_ID,CERTIFICATE_RECORD.SCORE,CERTIFICATE_RECORD.SCORE_LEVEL,
                                    CERTIFICATE_RECORD.PASS_STATUS,CERTIFICATE_RECORD.ISSUE_TIME,CERTIFICATE_RECORD.VALID_DATE,
                                    CERTIFICATE_RECORD.NAME,CERTIFICATE_RECORD.PASS_TIME)
                            .end())
                    .from(CERTIFICATE_RECORD)
                    .where(conditions)
                    .limit((page - 1) * pageSize, pageSize)
                    .fetchInto(CertificateRecord.class);
        });
    }


    @Override
    public List<CertificateRecord> findMemberCertificate(String memebrId, String examId) {
        List<CertificateRecord> certificateRecords = certificateRecordDao.execute(w -> w.select(Fields.start()
                                .add(CERTIFICATE_RECORD.NUM, CERTIFICATE_RECORD.ISSUE_TIME,  CERTIFICATE_RECORD.PASS_TIME).end())
                        .from(CERTIFICATE_RECORD))
                .where(CERTIFICATE_RECORD.MEMBER_ID.eq(memebrId))
                .and(CERTIFICATE_RECORD.EXAM_ID.eq(examId))
                .fetch().stream().map(r ->{
                    CertificateRecord certificateRecord = new CertificateRecord();
                    certificateRecord.setNum(r.getValue(CERTIFICATE_RECORD.NUM));
                    certificateRecord.setIssueTime(r.getValue(CERTIFICATE_RECORD.ISSUE_TIME));
                    certificateRecord.setPassTime(r.getValue(CERTIFICATE_RECORD.PASS_TIME));

                    return certificateRecord;
                }).collect(Collectors.toList());
        return certificateRecords;
    }

    @Override
    public Map<String, String> findByMemberIdsAndExamIds(List<String> memberIds, List<String> examIds) {
        Map<String, String> map = new HashMap<>();
        certificateRecordDao.execute(x -> x.select(Fields.start()
                        .add(CERTIFICATE_RECORD.ID)
                        .add(CERTIFICATE_RECORD.MEMBER_ID)
                        .add(CERTIFICATE_RECORD.EXAM_ID)
                        .end())
                .from(CERTIFICATE_RECORD)
                .where(CERTIFICATE_RECORD.EXAM_ID.in(examIds))
                .and(CERTIFICATE_RECORD.MEMBER_ID.in(memberIds))
                .fetch().stream().map(r -> {
                    map.put(r.getValue(CERTIFICATE_RECORD.EXAM_ID)+"#"+r.getValue(CERTIFICATE_RECORD.MEMBER_ID),r.getValue(CERTIFICATE_RECORD.ID));
                    return null;
                }).collect(Collectors.toList()));
        return map;
    }

    @Override
    public Map<String, List<Exam>> getCertificateIdMap(List<String> examIds, String subId) {
        Map<String, List<Exam>> map = examDao.execute(x -> x.select(Fields.start()
                        .add(EXAM.ID)
                        .add(EXAM.NAME)
                        .add(EXAM.HAS_CERT)
                        .add(EXAM.CERTIFICATE_ID)
                        .end())
                .from(EXAM)
                .leftJoin(SUB_AUTHENTICATED_EXAM_GROUP).on(EXAM.ID.eq(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID))
                .where(EXAM.ID.in(examIds))
                .and(SUB_AUTHENTICATED_EXAM_GROUP.SUB_AUTHENTICATED_ID.eq(subId))
                .fetch().stream().distinct().map(r -> {
                    Exam exam = new Exam();
                    exam.setId(r.getValue(EXAM.ID));
                    exam.setName(r.getValue(EXAM.NAME));
                    exam.setHasCert(r.getValue(EXAM.HAS_CERT));
                    exam.setCertificateId(r.getValue(EXAM.CERTIFICATE_ID));
                    return exam;
                }).collect(Collectors.groupingBy(Exam::getId)));
        return map;
    }

    @Override
    public Map<String, List<AudienceObject>> getExamItemMap(List<String> examIds) {
        Map<String, List<AudienceObject>> map = examDao.execute(x -> x.select(Fields.start()
                        .add(AUDIENCE_OBJECT.TARGET_ID)
                        .add(AUDIENCE_OBJECT.ITEM_ID)
                        .end())
                .from(AUDIENCE_OBJECT)
                .where(AUDIENCE_OBJECT.TARGET_ID.in(examIds))
                .fetch().stream().map(r -> {
                    AudienceObject audienceObject = new AudienceObject();
                    audienceObject.setTargetId(r.getValue(AUDIENCE_OBJECT.TARGET_ID));
                    audienceObject.setItemId(r.getValue(AUDIENCE_OBJECT.ITEM_ID));
                    return audienceObject;
                }).collect(Collectors.groupingBy(AudienceObject::getTargetId)));
        return map;
    }

    @Override
    public Map<String, List<AudienceMember>> getMemberItemMap(List<String> memberIds) {
        Map<String, List<AudienceMember>> map = examDao.execute(x -> x.select(Fields.start()
                        .add(AUDIENCE_MEMBER.MEMBER_ID)
                        .add(AUDIENCE_MEMBER.ITEM_ID)
                        .end())
                .from(AUDIENCE_MEMBER)
                .where(AUDIENCE_MEMBER.MEMBER_ID.in(memberIds))
                .fetch().stream().map(r -> {
                    AudienceMember audienceMember = new AudienceMember();
                    audienceMember.setMemberId(r.getValue(AUDIENCE_MEMBER.MEMBER_ID));
                    audienceMember.setItemId(r.getValue(AUDIENCE_MEMBER.ITEM_ID));
                    return audienceMember;
                }).collect(Collectors.groupingBy(AudienceMember::getMemberId)));
        return map;
    }

    @Override
    public Map<String, String> findGridByMemberIdsAndExamIds(List<String> memberIds, List<String> examIds) {
        Map<String, String> map = new HashMap<>();
        certificateRecordDao.execute(x -> x.selectDistinct(Fields.start()
                        .add(CERTIFICATE_RECORD.MEMBER_ID)
                        .add(CERTIFICATE_RECORD.PROFESSION_LEVEL_ID)
                        .end())
                .from(CERTIFICATE_RECORD)
                .where(CERTIFICATE_RECORD.EXAM_ID.in(examIds))
                .and(CERTIFICATE_RECORD.MEMBER_ID.in(memberIds))
                .and(CERTIFICATE_RECORD.GRID.eq(CertificateRecord.IS_GRID_YES))
                .fetch().stream().map(r -> {
                    map.put(r.getValue(CERTIFICATE_RECORD.MEMBER_ID)+"#"+r.getValue(CERTIFICATE_RECORD.PROFESSION_LEVEL_ID),r.getValue(CERTIFICATE_RECORD.PROFESSION_LEVEL_ID));
                    return null;
                }).collect(Collectors.toList()));
        return map;
    }


    @Override
    public List<CertificateRecord> findExamCertificateRecordList(String examId, Long startTime, Long endTime) {
        return certificateRecordDao.execute(w -> w.select(Fields.start()
                                .add(CERTIFICATE_RECORD.ID,CERTIFICATE_RECORD.NUM, CERTIFICATE_RECORD.PASS_TIME,CERTIFICATE_RECORD.EXAM_ID,
                                        CERTIFICATE_RECORD.MEMBER_ID, CERTIFICATE_RECORD.CREATE_TIME).end())
                        .from(CERTIFICATE_RECORD))
                .where(CERTIFICATE_RECORD.EXAM_ID.eq(examId))
                .and(CERTIFICATE_RECORD.CREATE_TIME.between(startTime, endTime))
                .fetchInto(CertificateRecord.class);
    }

}

package com.zxy.product.exam.service.support;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.exam.annotation.DataSource;
import com.zxy.product.exam.api.AnswerRecordService;
import com.zxy.product.exam.api.MarkConfigService;
import com.zxy.product.exam.api.QuestionCopyService;
import com.zxy.product.exam.entity.AnswerRecord;
import com.zxy.product.exam.entity.ExamRecord;
import com.zxy.product.exam.entity.MarkConfig;
import com.zxy.product.exam.entity.PaperInstanceQuestionCopy;
import com.zxy.product.exam.entity.Question;
import com.zxy.product.exam.entity.QuestionAttr;
import com.zxy.product.exam.entity.QuestionAttrCopy;
import com.zxy.product.exam.entity.QuestionCopy;
import com.zxy.product.exam.service.util.GetTableUtil;
import org.jooq.Condition;
import org.jooq.Record;
import org.jooq.Result;
import org.jooq.SelectOnConditionStep;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.zxy.product.exam.jooq.Tables.MARK_RECORD;
import static com.zxy.product.exam.jooq.Tables.QUESTION;
import static com.zxy.product.exam.jooq.Tables.QUESTION_ATTR;
import static com.zxy.product.exam.jooq.Tables.QUESTION_ATTR_COPY;
import static com.zxy.product.exam.jooq.Tables.QUESTION_COPY;

@Service
public class QuestionCopyServiceSupport implements QuestionCopyService{


    private CommonDao<QuestionCopy> dao;

    private CommonDao<Question> questionDao;

    private MarkConfigService markConfigService;

    private Cipher cipher = null;

    private AnswerRecordService answerRecordService;

    @Autowired
    public void setAnswerRecordService(AnswerRecordService answerRecordService) {
        this.answerRecordService = answerRecordService;
    }

    public QuestionCopyServiceSupport() throws NoSuchAlgorithmException, NoSuchPaddingException {
    	cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
	}

    private GetTableUtil getTableUtil;

    @Autowired
    public void setGetTableUtil(GetTableUtil getTableUtil) {
        this.getTableUtil = getTableUtil;
    }

    /**
     * ase加密需要向量
     */
    private final static String IV = "1234567890123456";

    @Autowired
    public void setDao(CommonDao<QuestionCopy> dao) {
        this.dao = dao;
    }

    @Autowired
    public void setQuestionDao(CommonDao<Question> questionDao) {
		this.questionDao = questionDao;
	}

    @Autowired
    public void setMarkConfigService(MarkConfigService markConfigService) {
        this.markConfigService = markConfigService;
    }

    private List<QuestionAttrCopy> findAttrs(String copyId) {
        List<QuestionAttrCopy> attrs = dao.execute(e -> {
            return e.select(QUESTION_ATTR_COPY.fields()).from(QUESTION_ATTR_COPY)
            .where(QUESTION_ATTR_COPY.QUESTION_COPY_ID.eq(copyId)).orderBy(QUESTION_ATTR_COPY.CREATE_TIME.asc())
            .fetchInto(QuestionAttrCopy.class);
        });
        return attrs;
    }

    @SuppressWarnings("unchecked")
    @Override
    @DataSource
    public List<QuestionCopy> findMarkPaperQuestion(
        String examId,
        String memberId,
        String examRecordId,
        String paperInstanceId) {

        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));
        TableImpl<?> paperInstanceQuestionCopyTable = getTableUtil.getPaperInstanceQuestionCopyTable(getTableUtil.getPaperInstanceQuestionCopyStringTable(examId));

        List<QuestionCopy> questions = null;

        int type = 1;
        Result<Record> records = null;

        SelectOnConditionStep<?> step = dao.execute(e ->
        e.selectDistinct(
                Fields.start()
                .add(paperInstanceQuestionCopyTable.fields())
                .add(table.fields())
                .add(QUESTION_COPY.ID)
                .add(QUESTION_COPY.TYPE)
                .add(QUESTION_COPY.CONTENT)
                .add(QUESTION_COPY.PARENT_ID)
                .add(QUESTION_COPY.SCORE)
                .add(QUESTION_ATTR_COPY.ID)
                .add(QUESTION_ATTR_COPY.NAME)
                .add(QUESTION_ATTR_COPY.VALUE)
                .add(QUESTION_ATTR_COPY.TYPE)
                .add(QUESTION_ATTR_COPY.QUESTION_COPY_ID)
                .end()

                )
        .from(paperInstanceQuestionCopyTable)

        .leftJoin(table).on(table.field("f_question_id", String.class).eq(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)))
        .and(table.field("f_exam_record_id", String.class).eq(examRecordId))

        .leftJoin(MARK_RECORD).on(MARK_RECORD.ANSWER_RECORD_ID.eq(table.field("f_id", String.class)))
        .and(MARK_RECORD.ID.isNull())

        .leftJoin(QUESTION_COPY).on(QUESTION_COPY.ID.eq(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)))

        .leftJoin(QUESTION_ATTR_COPY).on(QUESTION_ATTR_COPY.QUESTION_COPY_ID.eq(QUESTION_COPY.ID))
                );

		Condition c = paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class).eq(paperInstanceId).and(QUESTION_COPY.PARENT_ID.isNull()).and(table.field("f_score", Integer.class).isNull());

        switch (type) {
        case 1:
            records = (Result<Record>) step.where(c,
                QUESTION_COPY.TYPE.in(
                        Question.QUESTION_ANWSER,
                        Question.READING_COMPREHENSION)
            ).fetch();
            questions = getQuestionsByMap(paperInstanceId, records, examRecordId, Optional.of(1), examId);
            break;
//        case 2:
//            records = (Result<Record>) step.where(c, QUESTION_COPY.TYPE.in(markConfigs.stream().map(MarkConfig::getTypeId).collect(Collectors.toList()))
//            ).fetch();
//
//            questions = getQuestionsByMap(records);
//            break;
//        case 3:
//            records = (Result<Record>) step.where(c, QUESTION_COPY.ID.in(markConfigs.stream().map(MarkConfig::getTypeId).collect(Collectors.toList()))
//            ).fetch();
//
//            questions = getQuestionsByMap(records);
//            break;
        default:
            break;
        }
        Collections.sort(questions);
        return questions;
    }

    @SuppressWarnings("unchecked")
    @Override
    @DataSource
    public List<QuestionCopy> findMarkPaperQuestionByMember(
    		String examId,
    		String memberId,
    		String examRecordId,
    		String paperInstanceId) {

        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));
        TableImpl<?> paperInstanceQuestionCopyTable = getTableUtil.getPaperInstanceQuestionCopyTable(getTableUtil.getPaperInstanceQuestionCopyStringTable(examId));

    	List<QuestionCopy> questions = null;

        List<MarkConfig> markConfigs = markConfigService.find(examId, memberId);
        int type = markConfigs.get(0).getType();
    	Result<Record> records = null;

    	SelectOnConditionStep<?> step = dao.execute(e ->
    	e.selectDistinct(
    	        Fields.start()
    	        .add(paperInstanceQuestionCopyTable.fields())
    	        .add(table.fields())
    	        .add(QUESTION_COPY.ID)
    	        .add(QUESTION_COPY.TYPE)
    	        .add(QUESTION_COPY.CONTENT)
    	        .add(QUESTION_COPY.PARENT_ID)
    	        .add(QUESTION_COPY.SCORE)
    	        .add(QUESTION_ATTR_COPY.ID)
    	        .add(QUESTION_ATTR_COPY.NAME)
    	        .add(QUESTION_ATTR_COPY.VALUE)
    	        .add(QUESTION_ATTR_COPY.TYPE)
    	        .add(QUESTION_ATTR_COPY.QUESTION_COPY_ID)
    	        .end()
    	        )
    	.from(paperInstanceQuestionCopyTable)

    	.leftJoin(table).on(table.field("f_question_id", String.class).eq(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)))
    	.and(table.field("f_exam_record_id", String.class).eq(examRecordId))

    	.leftJoin(MARK_RECORD).on(MARK_RECORD.ANSWER_RECORD_ID.eq(table.field("f_id", String.class)))
    	.and(MARK_RECORD.ID.isNull())

    	.leftJoin(QUESTION_COPY).on(QUESTION_COPY.ID.eq(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)))

    	.leftJoin(QUESTION).on(QUESTION.ID.eq(QUESTION_COPY.QUESTION_ID))

    	.leftJoin(QUESTION_ATTR_COPY).on(QUESTION_ATTR_COPY.QUESTION_COPY_ID.eq(QUESTION_COPY.ID))
    	        );


    	Condition c = paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class).eq(paperInstanceId).and(QUESTION_COPY.PARENT_ID.isNull()).and(table.field("f_score", Integer.class).isNull());

    	switch (type) {
    	case 1:
    		records = (Result<Record>) step.where(c,QUESTION_COPY.TYPE.in(Question.QUESTION_ANWSER,Question.READING_COMPREHENSION)).fetch();
    		questions = getQuestionsByMap(paperInstanceId, records, examRecordId, Optional.of(1), examId);
    		break;
        case 2:
            records = (Result<Record>) step.where(c,QUESTION_COPY.TYPE.in(markConfigs.stream().map(MarkConfig::getTypeId).collect(Collectors.toList()))).fetch();
            questions = getQuestionsByMap(paperInstanceId, records, examRecordId, Optional.of(1), examId);
            break;
        case 3:
            records = (Result<Record>) step.where(c, QUESTION.ID.in(markConfigs.stream().map(MarkConfig::getTypeId).collect(Collectors.toList()))).fetch();
            questions = getQuestionsByMap(paperInstanceId, records, examRecordId, Optional.of(1), examId);
            break;
    	default:
    		break;
    	}
    	Collections.sort(questions);
    	return questions;
    }

    @SuppressWarnings("unchecked")
    @Override
    @DataSource
    public List<QuestionCopy> findAuditPaperQuestionByMember(
            String examId,
            String memberId,
            String examRecordId,
            String paperInstanceId) {

        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));
        TableImpl<?> paperInstanceQuestionCopyTable = getTableUtil.getPaperInstanceQuestionCopyTable(getTableUtil.getPaperInstanceQuestionCopyStringTable(examId));

        List<QuestionCopy> questions = null;

        List<MarkConfig> markConfigs = markConfigService.find(examId, memberId);
        int type = markConfigs.get(0).getType();
        Result<Record> records = null;

        SelectOnConditionStep<?> step = dao.execute(e ->
        e.selectDistinct(
                Fields.start()
                .add(paperInstanceQuestionCopyTable.fields())
                .add(table.fields())
                .add(QUESTION_COPY.ID)
                .add(QUESTION_COPY.TYPE)
                .add(QUESTION_COPY.CONTENT)
                .add(QUESTION_COPY.PARENT_ID)
                .add(QUESTION_COPY.SCORE)
                .add(QUESTION_ATTR_COPY.ID)
                .add(QUESTION_ATTR_COPY.NAME)
                .add(QUESTION_ATTR_COPY.VALUE)
                .add(QUESTION_ATTR_COPY.TYPE)
                .add(QUESTION_ATTR_COPY.QUESTION_COPY_ID)
                .end()
                )
        .from(paperInstanceQuestionCopyTable)

        .leftJoin(table).on(table.field("f_question_id", String.class).eq(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)))
        .and(table.field("f_exam_record_id", String.class).eq(examRecordId))

        .leftJoin(MARK_RECORD).on(MARK_RECORD.ANSWER_RECORD_ID.eq(table.field("f_id", String.class)))
        .and(MARK_RECORD.ID.isNull())

        .leftJoin(QUESTION_COPY).on(QUESTION_COPY.ID.eq(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)))

        .leftJoin(QUESTION).on(QUESTION.ID.eq(QUESTION_COPY.QUESTION_ID))

        .leftJoin(QUESTION_ATTR_COPY).on(QUESTION_ATTR_COPY.QUESTION_COPY_ID.eq(QUESTION_COPY.ID))
                );


        Condition c = paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class).eq(paperInstanceId)
                .and(QUESTION_COPY.PARENT_ID.isNull())
                .and(table.field("f_exam_record_id", String.class).eq(examRecordId));

        switch (type) {
        case 1:
            records = (Result<Record>) step.where(c,QUESTION_COPY.TYPE.eq(Question.SENTENCE_COMPLETION)).fetch();
            questions = getQuestionsByMap(paperInstanceId, records, examRecordId, Optional.of(1), examId);
            break;
        case 2:
            records = (Result<Record>) step.where(c,QUESTION_COPY.TYPE.in(markConfigs.stream().map(MarkConfig::getTypeId).collect(Collectors.toList()))).fetch();
            questions = getQuestionsByMap(paperInstanceId, records, examRecordId, Optional.of(1), examId);
            break;
        case 3:
            records = (Result<Record>) step.where(c, QUESTION.ID.in(markConfigs.stream().map(MarkConfig::getTypeId).collect(Collectors.toList()))).fetch();
            questions = getQuestionsByMap(paperInstanceId, records, examRecordId, Optional.of(1), examId);
            break;
        default:
            break;
        }
        Collections.sort(questions);
        return questions;
    }

    private List<QuestionCopy> getQuestionsByMap(String paperInstanceId, Result<Record> records, String examRecordId, Optional<Integer> searchScoreNull, String examId) {

        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));
        TableImpl<?> paperInstanceQuestionCopyTable = getTableUtil.getPaperInstanceQuestionCopyTable(getTableUtil.getPaperInstanceQuestionCopyStringTable(examId));

    	Map<String, QuestionAttrCopy> questionAttrCopyMap = records.into(QuestionAttrCopy.class)
                .stream().collect(Collectors.toMap(QuestionAttrCopy::getId, e -> e, (t1, t2) -> t2));

        Map<String, List<QuestionAttrCopy>> questionAttrCopyListMap = reduceQuestionAttrCopyList(questionAttrCopyMap.values());

        List<AnswerRecord> answerRecordList = records.stream().map(r -> {
            AnswerRecord answerRecord = new AnswerRecord();
            answerRecord.setId(r.getValue(table.field("f_id", String.class)));
            answerRecord.setCreateTime(r.getValue(table.field("f_create_time", Long.class)));
            answerRecord.setExamRecordId(r.getValue(table.field("f_exam_record_id", String.class)));
            answerRecord.setQuestionId(r.getValue(table.field("f_question_id", String.class)));
            answerRecord.setAnswer(r.getValue(table.field("f_answer", String.class)));
            answerRecord.setIsRight(r.getValue(table.field("f_is_right", Integer.class)));
            answerRecord.setScore(r.getValue(table.field("f_score", Integer.class)));
            return answerRecord;
        }).collect(Collectors.toList());

        Map<String, AnswerRecord> answerRecordMap = answerRecordList
           .stream().collect(Collectors.toMap(AnswerRecord::getQuestionId, e -> e, (k, v) -> v));

        List<PaperInstanceQuestionCopy> paperInstanceQuestionCopyList = records.stream().map(r -> {
            PaperInstanceQuestionCopy paperInstanceQuestionCopy = new PaperInstanceQuestionCopy();
            paperInstanceQuestionCopy.setId(r.getValue(paperInstanceQuestionCopyTable.field("f_id", String.class)));
            paperInstanceQuestionCopy.setPaperInstanceId(r.getValue(paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class)));
            paperInstanceQuestionCopy.setQuestionCopyId(r.getValue(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)));
            paperInstanceQuestionCopy.setCreateTime(r.getValue(paperInstanceQuestionCopyTable.field("f_create_time", Long.class)));
            paperInstanceQuestionCopy.setScore(r.getValue(paperInstanceQuestionCopyTable.field("f_score", Integer.class)));
            paperInstanceQuestionCopy.setSequence(r.getValue(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class)));
            return paperInstanceQuestionCopy;
        }).collect(Collectors.toList());

        Map<String, PaperInstanceQuestionCopy> paperInstanceQuestionCopys = paperInstanceQuestionCopyList.stream().collect(Collectors.toMap(PaperInstanceQuestionCopy::getQuestionCopyId, e -> e, (k, v) -> v));

        Map<String, Question> questions = records.into(Question.class)
        		.stream().collect(Collectors.toMap(Question::getId, e -> e, (k, v) -> v));

        Map<String, QuestionCopy> questionCopyMap = records.into(QuestionCopy.class)
            .stream().map(t -> {
            	if (questions.get(t.getQuestionId()) != null) {
            		t.setErrorRate(questions.get(t.getQuestionId()).getErrorRate());
            		t.setParsing(questions.get(t.getQuestionId()).getParsing());
            		t.setParsingText(questions.get(t.getQuestionId()).getParsingText());
            	}
                t.setQuestionAttrCopys(questionAttrCopyListMap.get(t.getId()));
                t.setAnswerRecord(answerRecordMap.get(t.getId()));
                t.setSequence(paperInstanceQuestionCopys.get(t.getId()).getSequence());
                return t;
        }).collect(Collectors.toMap(QuestionCopy::getId, e -> e, (k, v) -> v));

        Map<String, List<QuestionCopy>> subsMap = getSubsMap(paperInstanceId, questionCopyMap, examRecordId, searchScoreNull, examId);

        return questionCopyMap.values().stream().map(t -> {
            if (t.getType() == Question.READING_COMPREHENSION) {
                t.setSubs(subsMap.getOrDefault(t.getId(), new ArrayList<>()));
            }
            return t;
        }).collect(Collectors.toList());
    }

    private List<QuestionCopy> getQuestionsByMap2(String paperInstanceId, Result<Record> records, String examRecordId, Optional<Integer> searchScoreNull, String examId) {

    	Map<String, QuestionAttrCopy> questionAttrCopyMap = records.stream().map(t -> {
    		QuestionAttrCopy attr = new QuestionAttrCopy();
    		attr.setId(t.getValue(QUESTION_ATTR_COPY.ID.as("attrId")));
    		attr.setName(t.getValue(QUESTION_ATTR_COPY.NAME));
    		attr.setValue(t.getValue(QUESTION_ATTR_COPY.VALUE));
    		attr.setType(t.getValue(QUESTION_ATTR_COPY.TYPE));
    		attr.setQuestionCopyId(t.getValue(QUESTION_ATTR_COPY.QUESTION_COPY_ID));
    		return attr;
    	}).collect(Collectors.toMap(QuestionAttrCopy::getId, e -> e, (t1, t2) -> t2));

//    	Map<String, QuestionAttrCopy> questionAttrCopyMap = records.into(QUESTION_ATTR_COPY).into(QuestionAttrCopy.class)
//                .stream().collect(Collectors.toMap(QuestionAttrCopy::getId, e -> e, (t1, t2) -> t2));

    	Map<String, List<QuestionAttrCopy>> questionAttrCopyListMap = reduceQuestionAttrCopyList(questionAttrCopyMap.values());

    	TableImpl<?> paperInstanceQuestionCopyTable = getTableUtil.getPaperInstanceQuestionCopyTable(getTableUtil.getPaperInstanceQuestionCopyStringTable(examId));

        Map<String, AnswerRecord> answerRecordMap = records.into(AnswerRecord.class)
                .stream().collect(Collectors.toMap(AnswerRecord::getQuestionId, e -> e, (k, v) -> v));

        List<PaperInstanceQuestionCopy> paperInstanceQuestionCopyList = records.stream().map(r -> {
            PaperInstanceQuestionCopy paperInstanceQuestionCopy = new PaperInstanceQuestionCopy();
            paperInstanceQuestionCopy.setQuestionCopyId(r.getValue(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)));
            paperInstanceQuestionCopy.setSequence(r.getValue(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class)));
            return paperInstanceQuestionCopy;
        }).collect(Collectors.toList());

        Map<String, PaperInstanceQuestionCopy> paperInstanceQuestionCopys = paperInstanceQuestionCopyList.stream().collect(Collectors.toMap(PaperInstanceQuestionCopy::getQuestionCopyId, e -> e, (k, v) -> v));

    	Map<String, QuestionCopy> questionCopyMap = records.into(QuestionCopy.class)
    			.stream().map(t -> {
    				t.setQuestionAttrCopys(questionAttrCopyListMap.get(t.getId()));
    				t.setAnswerRecord(answerRecordMap.get(t.getId()));
    				t.setSequence(paperInstanceQuestionCopys.get(t.getId()).getSequence());
    				return t;
    			}).collect(Collectors.toMap(QuestionCopy::getId, e -> e, (k, v) -> v));

    	Map<String, List<QuestionCopy>> subsMap = getSubsMap(paperInstanceId, questionCopyMap, examRecordId, searchScoreNull, examId);

    	return questionCopyMap.values().stream().map(t -> {
    		if (t.getType() == Question.READING_COMPREHENSION) {
    			t.setSubs(subsMap.getOrDefault(t.getId(), new ArrayList<>()));
    		}
    		return t;
    	}).collect(Collectors.toList());
    }

    /**
     * @param questionCopyMap
     * @param examId
     * @return
     * 阅读理解 子题目
     * searchScoreNull： 兼容评卷API 过滤分数没有或者未答题目
     */
    private Map<String, List<QuestionCopy>> getSubsMap(String paperInstanceId, Map<String, QuestionCopy> questionCopyMap, String examRecordId, Optional<Integer> searchScoreNull, String examId) {

        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));
        TableImpl<?> paperInstanceQuestionCopyTable = getTableUtil.getPaperInstanceQuestionCopyTable(getTableUtil.getPaperInstanceQuestionCopyStringTable(examId));

        List<QuestionCopy> readings = questionCopyMap.values().stream().filter(t -> {
            return t.getType() == Question.READING_COMPREHENSION;
        }).collect(Collectors.toList());

        if (readings.size() > 0) {

            Result<Record> readingRecords = dao.execute(e ->
            e.select(
                    Fields.start()
                    .add(QUESTION_COPY.ID)
                    .add(QUESTION_COPY.TYPE)
                    .add(QUESTION_COPY.CONTENT)
                    .add(QUESTION_COPY.PARENT_ID)
                    .add(QUESTION_COPY.SCORE)
                    .add(QUESTION_COPY.QUESTION_ID)
                    .add(QUESTION.ID)
                    .add(QUESTION.ERROR_RATE)
                    .add(QUESTION.PARSING_TEXT)
                    .add(QUESTION.PARSING)
                    .add(table.fields())
                    .add(QUESTION_ATTR_COPY.ID)
                    .add(QUESTION_ATTR_COPY.NAME)
                    .add(QUESTION_ATTR_COPY.VALUE)
                    .add(QUESTION_ATTR_COPY.TYPE)
                    .add(QUESTION_ATTR_COPY.QUESTION_COPY_ID)
                    .add(paperInstanceQuestionCopyTable.fields())
                    .end()
                    )
            .from(QUESTION_COPY)
            .leftJoin(QUESTION).on(QUESTION.ID.eq(QUESTION_COPY.QUESTION_ID))
            .leftJoin(table).on(table.field("f_question_id", String.class).eq(QUESTION_COPY.ID)).and(table.field("f_exam_record_id", String.class).eq(examRecordId))
            .leftJoin(paperInstanceQuestionCopyTable).on(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class).eq(QUESTION_COPY.ID)).and(paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class).eq(paperInstanceId))
            .leftJoin(QUESTION_ATTR_COPY).on(QUESTION_ATTR_COPY.QUESTION_COPY_ID.eq(QUESTION_COPY.ID))
            .where(QUESTION_COPY.PARENT_ID.in(readings.stream().map(QuestionCopy::getId).collect(Collectors.toList())),
                    searchScoreNull.map(t -> table.field("f_score", Integer.class).isNull()).orElse(DSL.trueCondition()))
            .orderBy(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class).asc())
            .fetch()
                    );

            Map<String, QuestionAttrCopy> questionAttrMap = readingRecords.into(QuestionAttrCopy.class)
                    .stream().collect(Collectors.toMap(QuestionAttrCopy::getId, e -> e, (k, v) -> v));

            Map<String, List<QuestionAttrCopy>> questionAttrListMap = reduceQuestionAttrCopyList(questionAttrMap.values());

            List<AnswerRecord> answerRecordList = readingRecords.stream().map(r -> {
                AnswerRecord answerRecord = new AnswerRecord();
                answerRecord.setId(r.getValue(table.field("f_id", String.class)));
                answerRecord.setCreateTime(r.getValue(table.field("f_create_time", Long.class)));
                answerRecord.setExamRecordId(r.getValue(table.field("f_exam_record_id", String.class)));
                answerRecord.setQuestionId(r.getValue(table.field("f_question_id", String.class)));
                answerRecord.setAnswer(r.getValue(table.field("f_answer", String.class)));
                answerRecord.setIsRight(r.getValue(table.field("f_is_right", Integer.class)));
                answerRecord.setScore(r.getValue(table.field("f_score", Integer.class)));
                return answerRecord;
            }).collect(Collectors.toList());

            Map<String, AnswerRecord> answerRecordMap = answerRecordList
                    .stream().collect(Collectors.toMap(AnswerRecord::getQuestionId, e -> e, (k, v) -> v));

            List<PaperInstanceQuestionCopy> paperInstanceQuestionCopyList = readingRecords.stream().map(r -> {
                PaperInstanceQuestionCopy paperInstanceQuestionCopy = new PaperInstanceQuestionCopy();
                paperInstanceQuestionCopy.setId(r.getValue(paperInstanceQuestionCopyTable.field("f_id", String.class)));
                paperInstanceQuestionCopy.setPaperInstanceId(r.getValue(paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class)));
                paperInstanceQuestionCopy.setQuestionCopyId(r.getValue(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)));
                paperInstanceQuestionCopy.setCreateTime(r.getValue(paperInstanceQuestionCopyTable.field("f_create_time", Long.class)));
                paperInstanceQuestionCopy.setScore(r.getValue(paperInstanceQuestionCopyTable.field("f_score", Integer.class)));
                paperInstanceQuestionCopy.setSequence(r.getValue(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class)));
                return paperInstanceQuestionCopy;
            }).collect(Collectors.toList());

            Map<String, PaperInstanceQuestionCopy> paperInstanceQuestionCopys = paperInstanceQuestionCopyList.stream().collect(Collectors.toMap(PaperInstanceQuestionCopy::getQuestionCopyId, e -> e, (k, v) -> v));

            Map<String, Question> questions = readingRecords.into(Question.class)
            		.stream().collect(Collectors.toMap(Question::getId, e -> e, (k, v) -> v));

            Map<String, QuestionCopy> questionMap = readingRecords.into(QuestionCopy.class)
            .stream().map(t -> {
            	if (questions.get(t.getQuestionId()) != null) {
            		t.setErrorRate(questions.get(t.getQuestionId()).getErrorRate());
            		t.setParsing(questions.get(t.getQuestionId()).getParsing());
                    t.setParsingText(questions.get(t.getQuestionId()).getParsingText());
            	}
            	t.setSequence(paperInstanceQuestionCopys.get(t.getId()).getSequence());
            	return t;
            }).collect(Collectors.toMap(QuestionCopy::getId, e -> e, (k, v) -> v));

            Map<String, List<QuestionCopy>> subsMap = subQuestionsKeyByParentId(answerRecordMap, questionMap.values(), questionAttrListMap);

            return subsMap;
        }
        return new HashMap<>();
    }

    private Map<String, List<QuestionAttrCopy>> reduceQuestionAttrCopyList(Collection<QuestionAttrCopy> questionAttrCopies) {

        Map<String, List<QuestionAttrCopy>> map = new HashMap<>();
        questionAttrCopies.stream().forEach(questionAttr -> {
            if (map.get(questionAttr.getQuestionCopyId()) == null) {
            	map.put(questionAttr.getQuestionCopyId(), new ArrayList<>());
            }
            map.get(questionAttr.getQuestionCopyId()).add(questionAttr);
        });
        return map;
    }

    private Map<String, List<QuestionAttr>> reduceQuestionAttrList(Collection<QuestionAttr> questionAttrs) {
        Map<String, List<QuestionAttr>> map = new HashMap<>();
        questionAttrs.stream().forEach(questionAttr -> {
            if (map.get(questionAttr.getQuestionId()) == null) {
                map.put(questionAttr.getQuestionId(), new ArrayList<>());
            }
            map.get(questionAttr.getQuestionId()).add(questionAttr);
        });
        return map;
    }

    private Map<String, List<QuestionCopy>> subQuestionsKeyByParentId(Map<String, AnswerRecord> answerRecordMap, Collection<QuestionCopy> questionCopies,
            Map<String, List<QuestionAttrCopy>> questionAttrListMap) {

        Map<String, List<QuestionCopy>> map = new HashMap<>();
        questionCopies.stream().forEach(questionCopy -> {
            questionCopy.setQuestionAttrCopys(questionAttrListMap.get(questionCopy.getId()));
            if (map.get(questionCopy.getParentId()) == null) {
            	map.put(questionCopy.getParentId(), new ArrayList<>());
            }
            questionCopy.setAnswerRecord(answerRecordMap.get(questionCopy.getId()));
            map.get(questionCopy.getParentId()).add(questionCopy);
        });
        return map;
    }


    @Override
    @DataSource
    public List<String> findNewEmployeeExam(Long startTime, Long endTime, String examId, String paperInstanceId, Integer page, Integer pageSize) {
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
        return dao.execute(e->e.select(examRecordTable.field("f_member_id", String.class)).from(examRecordTable)
                .where(examRecordTable.field("f_paper_instance_id", String.class).eq(paperInstanceId),
                        examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT),
                        examRecordTable.field("f_is_finished", Integer.class).eq(ExamRecord.IS_FINISHED),
                        examRecordTable.field("f_last_submit_time", Long.class).ge(startTime),
                        examRecordTable.field("f_last_submit_time", Long.class).le(endTime))
                .limit((page - 1) * pageSize, pageSize)
                .fetch(examRecordTable.field("f_member_id", String.class))
        );
    }

    /**
     * 单独处理试题信息
     * @param result
     * @return
     */
    private Map<String, QuestionCopy> getQuestionCopy(Result<Record> result, String examId){
        TableImpl<?> paperInstanceQuestionCopyTable = getTableUtil.getPaperInstanceQuestionCopyTable(getTableUtil.getPaperInstanceQuestionCopyStringTable(examId));
        //查询出试卷
        List<PaperInstanceQuestionCopy> paperInstanceQuestionCopyList = result.stream().map(r -> {
            PaperInstanceQuestionCopy paperInstanceQuestionCopy = new PaperInstanceQuestionCopy();
            paperInstanceQuestionCopy.setQuestionCopyId(r.getValue(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class)));
            paperInstanceQuestionCopy.setSequence(r.getValue(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class)));
            return paperInstanceQuestionCopy;
        }).collect(Collectors.toList());
        Map<String, PaperInstanceQuestionCopy> paperInstanceQuestionCopys = paperInstanceQuestionCopyList.stream().collect(Collectors.toMap(PaperInstanceQuestionCopy::getQuestionCopyId, e -> e, (k, v) -> v));
        //查询出选项
        Map<String, QuestionAttrCopy> questionAttrCopyMap = result.into(QuestionAttrCopy.class)
                .stream().collect(Collectors.toMap(QuestionAttrCopy::getId, e -> e, (t1, t2) -> t2));

        Map<String, List<QuestionAttrCopy>> questionAttrCopyListMap = reduceQuestionAttrCopyList(questionAttrCopyMap.values());

        //查询试题,并设置试题顺序,试题选项
        Map<String, QuestionCopy> questionCopyMap = result.into(QuestionCopy.class)
                .stream().map(t -> {
                    t.setSequence(paperInstanceQuestionCopys.get(t.getId()).getSequence());
                    t.setQuestionAttrCopys(questionAttrCopyListMap.get(t.getId()));
                    return t;
                }).collect(Collectors.toMap(QuestionCopy::getId, e -> e, (k, v) -> v));
        return questionCopyMap;
    }

    @Override
    @DataSource
    public Map<String, ExamRecord> findQuestionsByPaperAndExamId(String paperInstanceId, String examId, List<String> memberIds) {
        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
        TableImpl<?> paperInstanceQuestionCopyTable = getTableUtil.getPaperInstanceQuestionCopyTable(getTableUtil.getPaperInstanceQuestionCopyStringTable(examId));

        //查询试题排序，试题答案
        Result<Record> result = dao.execute(e -> e.select(Fields.start()
                                .add(paperInstanceQuestionCopyTable.fields())
                                .add(QUESTION_COPY.ID,QUESTION_COPY.TYPE,QUESTION_COPY.CONTENT)
                                 .add(QUESTION_ATTR_COPY.fields())
                                .end()
                        ).from(paperInstanceQuestionCopyTable)
                        .leftJoin(QUESTION_COPY).on(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class).eq(QUESTION_COPY.ID))
                        .leftJoin(QUESTION_ATTR_COPY).on(QUESTION_ATTR_COPY.QUESTION_COPY_ID.eq(QUESTION_COPY.ID))
                        .where(paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class).eq(paperInstanceId), QUESTION_COPY.PARENT_ID.isNull())
                        .orderBy(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class).asc())
                        .fetch()
        );

        Map<String, QuestionCopy> questionCopy = getQuestionCopy(result, examId);

        //查询考试记录与答题记录
        Result<Record> records = dao.execute(e ->
                e.select(
                                Fields.start()
                                        .add(table.fields())
                                        .add(examRecordTable.fields())
                                        .end()
                        )
                        .from(examRecordTable)
                        .leftJoin(table).on(examRecordTable.field("f_id", String.class).eq(table.field("f_exam_record_id", String.class)))
                        .where(examRecordTable.field("f_paper_instance_id", String.class).eq(paperInstanceId),
                                examRecordTable.field("f_member_id", String.class).in(memberIds))
                        .fetch()
        );
        return getQuestionsByMap(records, examId, questionCopy);
    }

    private Map<String, ExamRecord> getQuestionsByMap( Result<Record> records, String examId,Map<String, QuestionCopy> questionCopy) {
        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        //一个人，只能考一次，只适用新员工考试
        List<ExamRecord> examRecords = records.stream().map(r -> {
            ExamRecord record = new ExamRecord();
            record.setId(r.getValue(examRecordTable.field("f_id", String.class)));
            record.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
            return record;
        }).collect(Collectors.toList());

        Map<String, ExamRecord> examRecordMap = examRecords.stream().collect(Collectors.toMap(ExamRecord::getMemberId, e -> e, (t1, t2) -> t2));

        List<AnswerRecord> answerRecordList = records.stream().map(r -> {
            AnswerRecord answerRecord = new AnswerRecord();
            answerRecord.setId(r.getValue(table.field("f_id", String.class)));
            answerRecord.setCreateTime(r.getValue(table.field("f_create_time", Long.class)));
            answerRecord.setExamRecordId(r.getValue(table.field("f_exam_record_id", String.class)));
            answerRecord.setQuestionId(r.getValue(table.field("f_question_id", String.class)));
            answerRecord.setAnswer(r.getValue(table.field("f_answer", String.class)));
            answerRecord.setIsRight(r.getValue(table.field("f_is_right", Integer.class)));
            answerRecord.setScore(r.getValue(table.field("f_score", Integer.class)));
            return answerRecord;
        }).collect(Collectors.toList());

        //根据考试记录分组，查询该考试记录所有的考试答案，这个默认一个人一个考试记录，只适用新员工考试
        Map<String, List<AnswerRecord>> answerMap = answerRecordList
                .stream().collect(Collectors.groupingBy(AnswerRecord::getExamRecordId));

        Map<String, ExamRecord> recordMap = new HashMap<>();
        //根据人，重新组合数据
        examRecordMap.forEach((k,v)->{
            List<QuestionCopy> questionCopies = dealAnswer(answerMap, v, questionCopy);
            ExamRecord record = new ExamRecord();
            record.setId(v.getId());
            record.setMemberId(v.getMemberId());
            record.setQuestionCopyList(questionCopies);
            recordMap.put(k, record);
        });
        return recordMap;
    }

    private List<QuestionCopy> dealAnswer(Map<String, List<AnswerRecord>> answerMap, ExamRecord record,Map<String, QuestionCopy> questionCopy){
        List<QuestionCopy> questions = new ArrayList<>();
        //得到考试记录的所有答题记录
        List<AnswerRecord> answerRecords = answerMap.get(record.getId());
        //给每个答题记录设置题目
        answerRecords.forEach(item->{
            QuestionCopy question = questionCopy.get(item.getQuestionId());
            QuestionCopy newQuestion = new QuestionCopy();
            //避免数据被覆盖
            BeanUtils.copyProperties(question, newQuestion);
            newQuestion.setAnswerRecord(item);
            questions.add(newQuestion);
        });
        return questions;
    }

    @Override
    public List<QuestionCopy> findQuestionsByPaperAndExamRecord(String paperInstanceId, String examRecordId, String examId) {

        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
        TableImpl<?> paperInstanceQuestionCopyTable = getTableUtil.getPaperInstanceQuestionCopyTable(getTableUtil.getPaperInstanceQuestionCopyStringTable(examId));

        Result<Record> records = dao.execute(e ->
        e.select(
                Fields.start()
                .add(QUESTION_COPY.ID)
                .add(QUESTION_COPY.TYPE)
                .add(QUESTION_COPY.CONTENT)
                .add(QUESTION_COPY.PARENT_ID)
                .add(QUESTION_COPY.SCORE)
                .add(QUESTION_COPY.QUESTION_ID)
                .add(QUESTION.ID)
                .add(QUESTION.ERROR_RATE)
                .add(QUESTION.PARSING_TEXT)
                .add(QUESTION.PARSING)
                .add(table.fields())
                .add(QUESTION_ATTR_COPY.ID)
                .add(QUESTION_ATTR_COPY.NAME)
                .add(QUESTION_ATTR_COPY.VALUE)
                .add(QUESTION_ATTR_COPY.TYPE)
                .add(QUESTION_ATTR_COPY.QUESTION_COPY_ID)
                .add(paperInstanceQuestionCopyTable.fields())
                .end()
                )
        .from(paperInstanceQuestionCopyTable)
        .leftJoin(QUESTION_COPY).on(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class).eq(QUESTION_COPY.ID))
        .leftJoin(QUESTION).on(QUESTION.ID.eq(QUESTION_COPY.QUESTION_ID))
        .leftJoin(table).on(QUESTION_COPY.ID.eq(table.field("f_question_id", String.class)).and(table.field("f_exam_record_id", String.class).eq(examRecordId)))
        .leftJoin(examRecordTable).on(examRecordTable.field("f_id", String.class).eq(table.field("f_exam_record_id", String.class))).and(examRecordTable.field("f_id", String.class).eq(examRecordId))
        .leftJoin(QUESTION_ATTR_COPY).on(QUESTION_ATTR_COPY.QUESTION_COPY_ID.eq(QUESTION_COPY.ID))
        .where(paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class).eq(paperInstanceId),QUESTION_COPY.PARENT_ID.isNull())
        .orderBy(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class).asc())
        .fetch()
                );

        List<QuestionCopy> questions = getQuestionsByMap(paperInstanceId, records, examRecordId, Optional.empty(), examId);
        Collections.sort(questions);
        return questions;
    }

	@Override
	public String calculateRecoveryAmount(String examRecordId) {
//		ExamRecord examRecord = examRecordDao.get(examRecordId);
//		Map<String, Integer> map = dao.execute(e ->
//			e.select(
//				QUESTION_RECOVERY.ID.countDistinct(),
//				QUESTION_RECOVERY.QUESTION_ID
//		    )
//		   .from(QUESTION_RECOVERY)
//		   .leftJoin(QUESTION_COPY).on(QUESTION_COPY.ID.eq(QUESTION_RECOVERY.QUESTION_ID))
//		   .where(QUESTION_COPY.EXAM_ID.eq(examRecord.getExamId()))
//		   .groupBy(QUESTION_RECOVERY.QUESTION_ID).fetch()
//		).stream().collect(HashMap::new, (m, v) ->
//			m.put(
//				v.getValue(QUESTION_RECOVERY.QUESTION_ID),
//				v.getValue(QUESTION_RECOVERY.ID.countDistinct())),
//				HashMap::putAll
//		    );
//
//		List<QuestionCopy> questionCopyList = dao.fetch(QUESTION_COPY.ID.in(map.keySet()));
//		questionCopyList.forEach(q -> {
//			q.setRecoveryCount(map.get(q.getId()));
//		});
//
//		List<Question> questionList = questionDao.fetch(QUESTION.ID.in(
//		    questionCopyList.stream().map(QuestionCopy::getQuestionId).collect(Collectors.toList())));
//
//		questionList.forEach(q -> {
//			q.setRecoveryCount(q.getRecoveryCount() + questionCopyList.stream().filter(e ->{
//				return e.getQuestionId().equals(q.getId());
//			}).collect(Collectors.toList()).get(0).getRecoveryCount());
//		});
//
//		dao.update(questionCopyList);
//		questionDao.update(questionList);
//
//		return examRecordId;
		return null;
	}

	@Override
	public List<Question> calculateErrorRate(List<String> questionCopyIds) {
	    return null;

//		DecimalFormat decimalFormat = new DecimalFormat("#.##");
//
//		List<String> questionIds = dao.fetch(QUESTION_COPY.ID.in(questionCopyIds)).stream().map(QuestionCopy::getQuestionId).collect(Collectors.toSet()).stream().collect(Collectors.toList());

//		Map<String, Integer> answerTotalAmout = dao.execute(e ->
//			e.selectDistinct(
//				QUESTION.ID,
//				ANSWER_RECORD.ID.countDistinct()
//			)
//			.from(ANSWER_RECORD)
//			.leftJoin(QUESTION_COPY).on(ANSWER_RECORD.QUESTION_ID.eq(QUESTION_COPY.ID))
//			.leftJoin(QUESTION).on(QUESTION_COPY.QUESTION_ID.eq(QUESTION.ID))
//			.where(QUESTION_COPY.TYPE.notIn(
//					Question.QUESTION_ANWSER,
//					Question.SENTENCE_COMPLETION,
//					Question.READING_COMPREHENSION)
//			,QUESTION.ID.in(questionIds))
//			.groupBy(QUESTION.ID).fetch()
//		).stream().collect(
//			HashMap::new,
//			(m, v) -> m.put(v.getValue(QUESTION.ID),v.getValue(ANSWER_RECORD.ID.countDistinct())),
//			HashMap::putAll
//		);
//
//		Map<String, Integer> wrongAnswerAmount = dao.execute(e ->
//			e.selectDistinct(
//				QUESTION.ID,
//				ANSWER_RECORD.ID.countDistinct()
//			)
//			.from(ANSWER_RECORD)
//			.leftJoin(QUESTION_COPY).on(ANSWER_RECORD.QUESTION_ID.eq(QUESTION_COPY.ID))
//			.leftJoin(QUESTION).on(QUESTION_COPY.QUESTION_ID.eq(QUESTION.ID))
//			.where(QUESTION_COPY.TYPE.notIn(
//					Question.QUESTION_ANWSER,
//					Question.SENTENCE_COMPLETION,
//					Question.READING_COMPREHENSION
//				),
//				QUESTION.ID.in(questionIds)
//			)
//			.and(ANSWER_RECORD.IS_RIGHT.eq(AnswerRecord.WRONG))
//			.groupBy(QUESTION.ID).fetch()
//		).stream().collect(
//			HashMap::new,
//			(m, v) -> m.put(v.getValue(QUESTION.ID),v.getValue(ANSWER_RECORD.ID.countDistinct())),
//			HashMap::putAll
//		);
//
//		List<Question> questionList = dao.execute(e ->
//			e.select(QUESTION.fields())
//				.from(QUESTION)
//				.where(QUESTION.ID.in(answerTotalAmout.keySet()))
//				.fetch().stream().map(t -> {
//					Question question = t.into(QUESTION).into(Question.class);
//					Integer wrong = 0;
//					if ((wrong = wrongAnswerAmount.get(question.getId())) != null) {
//						double d = Double.parseDouble(
//							decimalFormat.format(
//								((double)wrong / answerTotalAmout.get(question.getId())) * Question.PERCENT
//							)
//						);
//						question.setErrorRate((int) (d * Question.TEN_THOUSAND));//乘以10000 前端渲染除以10000
//					} else {
//						question.setErrorRate(Question.ZERO_PERCENT);
//					}
//					return question;
//				}).collect(Collectors.toList())
//		);
//
//		List<QuestionCopy> questionCopyList = dao.execute(e ->
//			e.selectDistinct(QUESTION_COPY.fields())
//				.from(QUESTION_COPY)
//				.where(QUESTION_COPY.QUESTION_ID.in(answerTotalAmout.keySet()))
//				.fetch()
//				.stream().map(t -> {
//					QuestionCopy questionCopy = t.into(QUESTION_COPY).into(QuestionCopy.class);
//					questionCopy.setErrorRate(
//						questionList.stream().filter(f ->
//							f.getId().equals(questionCopy.getQuestionId()))
//								.collect(Collectors.toList()).get(0).getErrorRate()
//					);
//					return questionCopy;
//				}).collect(Collectors.toList())
//		);
//
////		questionDao.update(questionList);
//		batchUpdateQuestionErrorRate(questionList);
////		dao.update(questionCopyList);
//		batchUpdateQuestionCopyErrorRate(questionCopyList);
//		return questionList;
	}

    private void batchUpdateQuestionErrorRate(List<Question> questionList) {
		questionDao.execute(e -> {
			return e.batch(
				questionList.stream().map(t -> {
					return e.update(QUESTION).set(QUESTION.ERROR_RATE, t.getErrorRate()).where(QUESTION.ID.eq(t.getId()));
				}).collect(Collectors.toList())
			).execute();
		});
	}

    private void batchUpdateQuestionCopyErrorRate(List<QuestionCopy> questionCopyList) {
    	questionDao.execute(e -> {
    		return e.batch(
				questionCopyList.stream().map(t -> {
					return e.update(QUESTION_COPY).set(QUESTION_COPY.ERROR_RATE, t.getErrorRate()).where(QUESTION_COPY.ID.eq(t.getId()));
				}).collect(Collectors.toList())
			).execute();
    	});
    }

	/**
	 * 加密
	 * @param value
	 * @param k
	 * @return
	 * @throws Exception
	 */
	@Override
	public String encryptAnswer(String value, String k) throws Exception {
		Key keySpec = new SecretKeySpec(k.getBytes(), "AES");
		IvParameterSpec ivSpec = new IvParameterSpec(IV.getBytes());

		cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);

		byte[] byteResult = cipher.doFinal(value.getBytes());

		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < byteResult.length; i++) {
			String hex = Integer.toHexString(byteResult[i] & 0xFF);
			if (hex.length() == 1) {
				hex = '0' + hex;
			}
			sb.append(hex.toUpperCase());
		}
		return sb.toString();
	}

	@Override
	public List<QuestionCopy> findQuestionsByPaperId(String paperInstanceId, String examRecordId, String examId) {

	    TableImpl<?> paperInstanceQuestionCopyTable = getTableUtil.getPaperInstanceQuestionCopyTable(getTableUtil.getPaperInstanceQuestionCopyStringTable(examId));

		 Result<Record> records = dao.execute(e ->
	         e.selectDistinct(
	             Fields.start()
	             .add(QUESTION_COPY.ID)
	             .add(QUESTION_COPY.CONTENT)
	             .add(QUESTION_COPY.PARENT_ID)
	             .add(QUESTION_COPY.SCORE)
	             .add(QUESTION_COPY.TYPE)
	             .add(QUESTION_COPY.DIFFICULTY)
	             .add(QUESTION_ATTR_COPY.ID.as("attrId"))
	             .add(QUESTION_ATTR_COPY.NAME)
	             .add(QUESTION_ATTR_COPY.VALUE)
	             .add(QUESTION_ATTR_COPY.TYPE)
	             .add(QUESTION_ATTR_COPY.QUESTION_COPY_ID)
	             .add(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class))
	             .add(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class))
	             .end()
	         )
	         .from(QUESTION_COPY)
	         .leftJoin(paperInstanceQuestionCopyTable).on(paperInstanceQuestionCopyTable.field("f_question_copy_id", String.class).eq(QUESTION_COPY.ID))
	         .leftJoin(QUESTION_ATTR_COPY).on(QUESTION_ATTR_COPY.QUESTION_COPY_ID.eq(QUESTION_COPY.ID))
	         .where(paperInstanceQuestionCopyTable.field("f_paper_instance_id", String.class).eq(paperInstanceId),QUESTION_COPY.PARENT_ID.isNull())
	         .orderBy(paperInstanceQuestionCopyTable.field("f_sequence", Integer.class).asc())
	         .fetch()
     );

     List<QuestionCopy> questions = getQuestionsByMap2(paperInstanceId, records, examRecordId, Optional.empty(), examId);
     return questions;
	}

	@Override
	public List<Map<String, Object>> getQuestionAnswerList(List<QuestionCopy> questions) {
		return questions.stream().map(t -> {
			Map<String, Object> map = new HashMap<>();

			if (t.getType() == Question.READING_COMPREHENSION) {
				map.put("questionId", t.getId());
				map.put("answer", t.getSubs().stream().map(s -> {
					return getQuestionAnswerMap(s);
				}).collect(Collectors.toList()));
				map.put("type", t.getType());
				return map;
			} else {
				return getQuestionAnswerMap(t);
			}

		}).collect(Collectors.toList());
	}

	private Map<String, Object> getQuestionAnswerMap(QuestionCopy t) {
		Map<String, Object> map = new HashMap<>();

		map.put("questionId", t.getId());
		if (t.getType() == Question.SINGLE_CHOOSE || t.getType() == Question.MULTIPLE_CHOOSE) {
			map.put("answer", t.getQuestionAttrCopys().stream().filter(a -> Integer.valueOf(a.getType()) == QuestionAttr.ANSWER_TYPE).map(attr -> {
				Map<String, String> m = new HashMap<>();
				m.put("id", attr.getId());
				m.put("value", attr.getName());
				return m;
			}).collect(Collectors.toList()	));
		}

		if (t.getType() == Question.JDUGEMENT || t.getType() == Question.SENTENCE_COMPLETION || t.getType() == Question.QUESTION_ANWSER || t.getType() == Question.SORTING) {
			map.put("answer", t.getQuestionAttrCopys().get(0).getValue());
		}
		map.put("type", t.getType());
		return map;
	}

	@Override
	public List<QuestionCopy> findQuestionCopysByExamId(String examId) {
		Result<Record> records = dao.execute(e -> {
			return e.select(
				Fields.start()
				.add(QUESTION_COPY)
				.add(QUESTION_ATTR_COPY)
				.end()
			)
			.from(QUESTION_COPY)
			.leftJoin(QUESTION_ATTR_COPY).on(QUESTION_COPY.ID.eq(QUESTION_ATTR_COPY.QUESTION_COPY_ID))
			.where(QUESTION_COPY.EXAM_ID.eq(examId))
			.fetch();
		});

		Map<String, QuestionCopy> questionCopys = records.into(QuestionCopy.class).stream().collect(Collectors.toMap(QuestionCopy::getId, e -> e, (k, v) -> v));
		List<QuestionAttrCopy> questionAttrCopys = records.into(QuestionAttrCopy.class).stream().filter(f -> f.getId() != null).collect(Collectors.toList());

		Map<String, List<QuestionAttrCopy>> questionAttrCopyMap = questionAttrCopys.stream()
                                                                                   .collect(Collectors.groupingBy(QuestionAttrCopy::getQuestionCopyId));


		return questionCopys.values().stream().map(q -> {
			q.setQuestionAttrCopys(questionAttrCopyMap.get(q.getId()));
			return q;
		}).collect(Collectors.toList());
	}

    @Override
    public List<Question> findQuestionsTopError(List<String> questionDepotList) {
        List<Question> questionList = dao.execute(e -> e.select(
                Fields.start()
                .add(QUESTION.ID)
                .add(QUESTION.ERROR_RATE)
                .add(QUESTION.TYPE)
                .add(QUESTION.CONTENT)
                .add(QUESTION.DIFFICULTY)
                .add(QUESTION.SCORE)
                .end())
                .from(QUESTION)
                .where(QUESTION.QUESTION_DEPOT_ID.in(questionDepotList))
                .and(QUESTION.ERROR_RATE.isNotNull())
                .and(QUESTION.ERROR_RATE.ne(0))
                .and(QUESTION.ERROR_RATE.ne(1000000))
                .orderBy(QUESTION.ERROR_RATE.desc())
                .limit(0,10)
                .fetch(r -> {
                    Question question = new Question();
                    question.setId(r.getValue(QUESTION.ID));
                    question.setErrorRate(r.getValue(QUESTION.ERROR_RATE));
                    question.setType(r.getValue(QUESTION.TYPE));
                    question.setContent(r.getValue(QUESTION.CONTENT));
                    question.setDifficulty(r.getValue(QUESTION.DIFFICULTY));
                    question.setScore(r.getValue(QUESTION.SCORE));
                    return question;
                }));

        List<String> questionIds = questionList.stream().map(Question::getId).collect(Collectors.toList());

        Map<String, QuestionAttr> questionAttrMap = dao.execute(e -> e.select(
                Fields.start()
                .add(QUESTION_ATTR)
                .end())
                .from(QUESTION_ATTR)
                .where(QUESTION_ATTR.QUESTION_ID.in(questionIds))
                .fetchInto(QuestionAttr.class))
        .stream().collect(Collectors.toMap(QuestionAttr::getId, e -> e, (t1, t2) -> t2));

        Map<String, List<QuestionAttr>> questionAttrListMap = reduceQuestionAttrList(questionAttrMap.values());

        List<Question> list = questionList.stream().map(t -> {
            t.setQuestionAttrs(questionAttrListMap.get(t.getId()));
            return t;
        }).collect(Collectors.toList());

        return list;
    }

    @Override
    public List<Question> findAnswerRecord(String examRecordId, String examId) {

        TableImpl<?> table = getTableUtil.getAnswerRecordTable(getTableUtil.getAnswerRecordStringTable(examId));


        List<Question> questionList = dao.execute(e -> e.select(
                Fields.start()
                .add(table.fields())
                .add(QUESTION.ID)
                .end())
                .from(table)
                .leftJoin(QUESTION_COPY).on(table.field("f_question_id", String.class).eq(QUESTION_COPY.ID))
                .leftJoin(QUESTION).on(QUESTION.ID.eq(QUESTION_COPY.QUESTION_ID))
                .where(table.field("f_exam_record_id", String.class).eq(examRecordId))
                .fetch(r -> {
                    AnswerRecord answerRecord = new AnswerRecord();
                    answerRecord.setId(r.getValue(table.field("f_id", String.class)));
                    answerRecord.setCreateTime(r.getValue(table.field("f_create_time", Long.class)));
                    answerRecord.setExamRecordId(r.getValue(table.field("f_exam_record_id", String.class)));
                    answerRecord.setQuestionId(r.getValue(table.field("f_question_id", String.class)));
                    answerRecord.setAnswer(r.getValue(table.field("f_answer", String.class)));
                    answerRecord.setIsRight(r.getValue(table.field("f_is_right", Integer.class)));
                    answerRecord.setScore(r.getValue(table.field("f_score", Integer.class)));
                    Question question = new Question();
                    question.setId(r.getValue(QUESTION.ID));
                    question.setAnswerReocrd(answerRecord);
                    return question;
                }));

        return questionList;
    }

    @Override
    @DataSource
    public QuestionCopy getById(String id){
        return dao.get(id);
    }



}

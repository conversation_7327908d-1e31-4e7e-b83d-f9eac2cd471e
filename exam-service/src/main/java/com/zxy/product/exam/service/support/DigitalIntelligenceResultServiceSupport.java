package com.zxy.product.exam.service.support;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.exam.api.DigitalIntelligenceResultService;
import com.zxy.product.exam.entity.szfn.DigitalIntelligenceResult;
import org.jooq.Condition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.exam.jooq.Tables.DIGITAL_INTELLIGENCE_RESULT;

@Service
public class DigitalIntelligenceResultServiceSupport implements DigitalIntelligenceResultService {

    private CommonDao<DigitalIntelligenceResult> digitalIntelligenceResultDao;

    @Autowired
    public void setDigitalIntelligenceResultDao(CommonDao<DigitalIntelligenceResult> digitalIntelligenceResultDao) {
        this.digitalIntelligenceResultDao = digitalIntelligenceResultDao;
    }

    @Override
    public DigitalIntelligenceResult insert(DigitalIntelligenceResult digitalIntelligenceResult) {
        digitalIntelligenceResultDao.insert(digitalIntelligenceResult);
        return digitalIntelligenceResult;
    }

    @Override
    public DigitalIntelligenceResult update(DigitalIntelligenceResult digitalIntelligenceResult) {
        digitalIntelligenceResultDao.update(digitalIntelligenceResult);
        return digitalIntelligenceResult;
    }

    @Override
    public void updateView(Integer view, String id) {
        digitalIntelligenceResultDao.execute(e->e.update(DIGITAL_INTELLIGENCE_RESULT)
                .set(DIGITAL_INTELLIGENCE_RESULT.IS_VIEW, view)
                .where(DIGITAL_INTELLIGENCE_RESULT.ID.eq(id)).execute());
    }

    @Override
    public String delete(String id) {
        digitalIntelligenceResultDao.delete(id);
        return id;
    }

    @Override
    public Optional<DigitalIntelligenceResult> get(String id) {
        return digitalIntelligenceResultDao.fetchOne(DIGITAL_INTELLIGENCE_RESULT.ID.eq(id));
    }

    @Override
    public List<DigitalIntelligenceResult> findByMemberId(String memberId) {
        return digitalIntelligenceResultDao.fetch(DIGITAL_INTELLIGENCE_RESULT.MEMBER_ID.eq(memberId));
    }

    @Override
    public Optional<DigitalIntelligenceResult> getByMemberId(String memberId) {
        return digitalIntelligenceResultDao.execute(e->e.select(DIGITAL_INTELLIGENCE_RESULT.ID,DIGITAL_INTELLIGENCE_RESULT.MEMBER_ID,DIGITAL_INTELLIGENCE_RESULT.NAME,
                        DIGITAL_INTELLIGENCE_RESULT.ABILITY_TAGS,DIGITAL_INTELLIGENCE_RESULT.MEDIUM_TERM_PLAN,DIGITAL_INTELLIGENCE_RESULT.SKILL_IMPROVEMENT,
                        DIGITAL_INTELLIGENCE_RESULT.RECOMMENDED_COURSES_ID,DIGITAL_INTELLIGENCE_RESULT.SHORT_TERM_PLAN,DIGITAL_INTELLIGENCE_RESULT.STRENGTH_ENHANCEMENT)
                .from(DIGITAL_INTELLIGENCE_RESULT)
                .where(DIGITAL_INTELLIGENCE_RESULT.MEMBER_ID.eq(memberId))
                .limit(1)
                .fetchOptionalInto(DigitalIntelligenceResult.class));
    }

    @Override
    public PagedResult<DigitalIntelligenceResult> findPagedResult(Integer page, Integer pageSize, Optional<String> name, Optional<String> memberId) {
        return digitalIntelligenceResultDao.execute(e -> {
            List<Condition> conditions = Stream.of(
                    name.map(DIGITAL_INTELLIGENCE_RESULT.NAME::contains),
                    memberId.map(DIGITAL_INTELLIGENCE_RESULT.MEMBER_ID::eq)
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            int count = e.selectCount()
                    .from(DIGITAL_INTELLIGENCE_RESULT)
                    .where(conditions)
                    .fetchOne(0, Integer.class);

            List<DigitalIntelligenceResult> results = e.select()
                    .from(DIGITAL_INTELLIGENCE_RESULT)
                    .where(conditions)
                    .orderBy(DIGITAL_INTELLIGENCE_RESULT.CREATE_TIME.desc())
                    .limit((page - 1) * pageSize, pageSize)
                    .fetch(r -> r.into(DigitalIntelligenceResult.class));

            return PagedResult.create(count, results);
        });
    }

    @Override
    public List<DigitalIntelligenceResult> findByIds(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return Collections.emptyList();
        }
        return digitalIntelligenceResultDao.fetch(DIGITAL_INTELLIGENCE_RESULT.ID.in(ids));
    }

    @Override
    public void batchInsert(List<DigitalIntelligenceResult> digitalIntelligenceResults) {
        if (digitalIntelligenceResults != null && !digitalIntelligenceResults.isEmpty()) {
            for (DigitalIntelligenceResult result : digitalIntelligenceResults) {
                digitalIntelligenceResultDao.insert(result);
            }
        }
    }

    @Override
    public void batchDelete(List<String> ids) {
        if (ids != null && !ids.isEmpty()) {
            for (String id : ids) {
                digitalIntelligenceResultDao.delete(id);
            }
        }
    }
}
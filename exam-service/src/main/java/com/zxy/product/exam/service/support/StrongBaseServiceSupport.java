package com.zxy.product.exam.service.support;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.exam.annotation.DataSource;
import com.zxy.product.exam.api.AnswerRecordService;
import com.zxy.product.exam.api.StrongBaseService;
import com.zxy.product.exam.content.ErrorCode;
import com.zxy.product.exam.content.MessageHeaderContent;
import com.zxy.product.exam.content.MessageTypeContent;
import com.zxy.product.exam.entity.*;
import com.zxy.product.exam.service.util.GetTableUtil;
import com.zxy.product.exam.util.DesensitizationUtil;
import com.zxy.product.exam.util.EncryptUtil;
import org.jooq.Condition;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import static com.zxy.product.exam.jooq.Tables.*;


@Service
public class StrongBaseServiceSupport implements StrongBaseService {

    private CommonDao<Exam> examDao;
    private CommonDao<SubAuthenticatedExamGroup> examGroupDao;
    private CommonDao<Organization> organizationDao;
    private CommonDao<ExamRegist> examRegistCommonDao;
    private CommonDao<SubAuthenticatedCancelCertificateRecord> subAuthenticatedCancelCertificateRecordDao;
    private CommonDao<CertificateRecord> certificateRecordDao;
    private AnswerRecordService answerRecordService;
    private CommonDao<DeleteDataExam> deleteDataExamCommonDao;
    private MessageSender messageSender;
    private CommonDao<ExamRecord> examRecordDao;
    private Cache groupExamIdCache;

    private Cache examCache;
    private GetTableUtil getTableUtil;

    @Autowired
    public void setGetTableUtil(GetTableUtil getTableUtil) {
        this.getTableUtil = getTableUtil;
    }


    @Autowired
    public void setCacheService(CacheService cacheService){
        this.groupExamIdCache = cacheService.create("sub-auth-group", "exam-ids");
        this.examCache = cacheService.create("cacheExam", "examEntity");
    }


    @Autowired
    public void setExamRecordDao(CommonDao<ExamRecord> examRecordDao) {
        this.examRecordDao = examRecordDao;
    }


    @Autowired
    public void setMessageSender(MessageSender messageSender){
        this.messageSender=messageSender;
    }


    @Autowired
    public void setDeleteDataExamCommonDao(CommonDao<DeleteDataExam> deleteDataExamCommonDao) {
        this.deleteDataExamCommonDao = deleteDataExamCommonDao;
    }

    @Autowired
    public void setCertificateRecordDao(CommonDao<CertificateRecord> certificateRecordDao) {
        this.certificateRecordDao = certificateRecordDao;
    }

    @Autowired
    public void setSubAuthenticatedCancelCertificateRecordDao(CommonDao<SubAuthenticatedCancelCertificateRecord> subAuthenticatedCancelCertificateRecordDao) {
        this.subAuthenticatedCancelCertificateRecordDao = subAuthenticatedCancelCertificateRecordDao;
    }

    @Autowired
    public void setExamRegistCommonDao(CommonDao<ExamRegist> examRegistCommonDao) {
        this.examRegistCommonDao = examRegistCommonDao;
    }

    @Autowired
    public void setAnswerRecordService(AnswerRecordService answerRecordService) {
        this.answerRecordService = answerRecordService;
    }

    @Autowired
    public void setExamGroupDao(CommonDao<SubAuthenticatedExamGroup> examGroupDao) {
        this.examGroupDao = examGroupDao;
    }

    @Autowired
    public void setExamDao(CommonDao<Exam> examDao) {
        this.examDao = examDao;
    }

    @Autowired
    public void setOrganizationDao(CommonDao<Organization> organizationDao) {
        this.organizationDao = organizationDao;
    }



    @Override
    public PagedResult<Exam> find(Integer page, Integer pageSize, Map<String, Set<String>> grantOrganizationPathMap,
                                  Optional<String> name, Optional<Integer> status,
                                  Optional<String> organizationId, Optional<String> examIds, Optional<String> subId) {

        // 如果传了subId,说明当前查询为编辑子认证查询，需要查询出包含该子认证的考试
        List<String> businessIds = new ArrayList<>();
        if (subId.isPresent()) {
            businessIds = examGroupDao.execute(
                    dslContext -> dslContext.select(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID)
                            .from(SUB_AUTHENTICATED_EXAM_GROUP)
                            .where(SUB_AUTHENTICATED_EXAM_GROUP.SUB_AUTHENTICATED_ID.eq(subId.get()))
                            .fetch(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID));
        }

        List<Condition> conditions = Stream.of(
                name.map(EXAM.NAME::contains),
                status.map(s -> Optional.of(EXAM.STATUS.eq(s))).orElseGet(() -> Optional.of(EXAM.STATUS.in(Exam.EXAM_STATUS_3_4))),
                organizationId.map(id -> ORGANIZATION.PATH.startsWith(organizationDao.get(id).getPath())),
                Optional.of(EXAM.STRONG_BASE_FLAG.eq(Exam.STRONG_BASE_FLAG_0).or(EXAM.ID.in(businessIds))),
                Optional.of(EXAM.TYPE.in(Exam.EXAM_TYPE_1_2)),
                examIds.map(ids -> EXAM.ID.notIn(Arrays.stream(ids.split(",")).distinct().collect(Collectors.toList())))
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

        generateOrganizationConditions(grantOrganizationPathMap,conditions);

        int count = examDao.execute(d -> d.select(DSL.count(EXAM.ID))
                .from(EXAM)
                .leftJoin(ORGANIZATION).on(EXAM.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(conditions)
                .fetchOne(DSL.count(EXAM.ID)));

        return PagedResult.create(count, examDao.execute(d -> d.select(Fields.start()
                        .add(EXAM.ID)
                        .add(EXAM.NAME)
                        .add(EXAM.STATUS)
                        .add(ORGANIZATION.NAME).end())
                .from(EXAM)
                .leftJoin(ORGANIZATION).on(EXAM.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(conditions)
                .limit((page - 1) * pageSize, pageSize).fetch(r -> {
                    Exam exam = new Exam();
                    exam.setId(r.getValue(EXAM.ID));
                    exam.setName(r.getValue(EXAM.NAME));
                    exam.setStatus(r.getValue(EXAM.STATUS));
                    exam.setOrganizationName(r.getValue(ORGANIZATION.NAME));
                    return exam;
                })));

    }

    @Override
    public void insert(SubAuthenticatedExamGroup examGroup) {
        if (!ObjectUtils.isEmpty(examGroup) && examGroup.getBusinessId() != null) {
            String examId = examGroup.getBusinessId();
            List<String> ids = examGroupDao.execute(
                    dslContext -> dslContext.select(SUB_AUTHENTICATED_EXAM_GROUP.ID)
                            .from(SUB_AUTHENTICATED_EXAM_GROUP)
                            .where(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID.eq(examId))
                            .fetch(SUB_AUTHENTICATED_EXAM_GROUP.ID));
            if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(ids)) {
                examGroupDao.delete(ids);
            }
            examGroup.setIsPublish(0);
            examGroupDao.insert(examGroup);
            examDao.execute(dsl -> dsl.update(EXAM).set(EXAM.STRONG_BASE_FLAG, Exam.STRONG_BASE_FLAG_1).where(EXAM.ID.eq(examId))).execute();
            examCache.clear(examId);
        }
    }

    @Override
    public void deleteBySubId(String subId) {
        List<String> examIds = examGroupDao.execute(
                dslContext -> dslContext.select(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID)
                        .from(SUB_AUTHENTICATED_EXAM_GROUP)
                        .where(SUB_AUTHENTICATED_EXAM_GROUP.SUB_AUTHENTICATED_ID.eq(subId))
                        .fetch(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID));
        examGroupDao.delete(SUB_AUTHENTICATED_EXAM_GROUP.SUB_AUTHENTICATED_ID.eq(subId));
        examDao.execute(dsl -> dsl.update(EXAM).set(EXAM.STRONG_BASE_FLAG, Exam.STRONG_BASE_FLAG_0).where(EXAM.ID.in(examIds))).execute();
        examIds.forEach(examId -> examCache.clear(examId));
    }

    @Override
    public void updateGroupPublish(String subId, Integer publish) {
        examGroupDao.execute(dslDao -> dslDao.update(SUB_AUTHENTICATED_EXAM_GROUP)
                .set(SUB_AUTHENTICATED_EXAM_GROUP.IS_PUBLISH, publish)
                .where(SUB_AUTHENTICATED_EXAM_GROUP.SUB_AUTHENTICATED_ID.eq(subId))
                .execute());
    }

    @Override
    public Boolean examGroupBeUsed(Optional<String> id, List<String> distinctExamIds) {
        List<Condition> conditions = Stream.of(
                id.map(SUB_AUTHENTICATED_EXAM_GROUP.SUB_AUTHENTICATED_ID::ne),
                Optional.of(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID.in(distinctExamIds))
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

        List<String> ids = examGroupDao.execute(
                dslContext -> dslContext.select(SUB_AUTHENTICATED_EXAM_GROUP.ID)
                        .from(SUB_AUTHENTICATED_EXAM_GROUP)
                        .where(conditions)
                        .limit(1)
                        .fetch(SUB_AUTHENTICATED_EXAM_GROUP.ID));
        return com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(ids);
    }

    public ExamRecord getNewestRecord(String examId, String memberId) {

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));

        List<ExamRecord> examRecords = examRecordDao.execute(e -> {
            return e.select(
                            Fields.start()
                                    .add(examRecordTable.field("f_id", String.class))
                                    .add(examRecordTable.field("f_create_time", Long.class))
                                    .add(examRecordTable.field("f_start_time", Long.class))
                                    .add(examRecordTable.field("f_end_time", Long.class))
                                    .add(examRecordTable.field("f_submit_time", Long.class))
                                    .add(examRecordTable.field("f_last_submit_time", Long.class))
                                    .add(examRecordTable.field("f_is_reset", Integer.class))
                                    .add(examRecordTable.field("f_status", Integer.class))
                                    .add(examRecordTable.field("f_order_content", String.class))
                                    .add(examRecordTable.field("f_member_id", String.class))
                                    .add(examRecordTable.field("f_exam_id", String.class))
                                    .add(examRecordTable.field("f_paper_instance_id", String.class))
                                    .add(examRecordTable.field("f_exam_times", Integer.class))
                                    .add(examRecordTable.field("f_personal_code", Integer.class))
                                    .add(examRecordTable.field("f_face_status", Integer.class))
                                    .end()
                    )
                    .from(examRecordTable)
                    .where(
                            examRecordTable.field("f_exam_id", String.class).eq(examId),
                            examRecordTable.field("f_member_id", String.class).eq(memberId),
                            examRecordTable.field("f_is_current", Integer.class).eq(ExamRecord.CURRENT)
                    ).orderBy(examRecordTable.field("f_create_time", Long.class).desc()).limit(0, 1).fetch(r -> {
                        ExamRecord examRecord = new ExamRecord();
                        examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
                        examRecord.setCreateTime(r.getValue(examRecordTable.field("f_create_time", Long.class)));
                        examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
                        examRecord.setEndTime(r.getValue(examRecordTable.field("f_end_time", Long.class)));
                        examRecord.setSubmitTime(r.getValue(examRecordTable.field("f_submit_time", Long.class)));
                        examRecord.setLastSubmitTime(r.getValue(examRecordTable.field("f_last_submit_time", Long.class)));
                        examRecord.setIsReset(r.getValue(examRecordTable.field("f_is_reset", Integer.class)));
                        examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
                        examRecord.setOrderContent(r.getValue(examRecordTable.field("f_order_content", String.class)));
                        examRecord.setMemberId(r.getValue(examRecordTable.field("f_member_id", String.class)));
                        examRecord.setExamId(r.getValue(examRecordTable.field("f_exam_id", String.class)));
                        examRecord.setPaperInstanceId(r.getValue(examRecordTable.field("f_paper_instance_id", String.class)));
                        examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
                        examRecord.setPersonalCode(r.getValue(examRecordTable.field("f_personal_code", Integer.class)));
                        examRecord.setFaceStatus(r.getValue(examRecordTable.field("f_face_status", Integer.class)));
                        return examRecord;
                    });
        });

        if (examRecords.size() > 0) return examRecords.get(0);

        return null;
    }

    @Override
    public Boolean examAgain(String examId, String memberId) {
        // 获取用户最新考试记录
        ExamRecord examRecord = getNewestRecord(examId, memberId);
        // 进行中的考试记录直接返回
        if (examRecord != null && examRecord.getStatus() != null && ExamRecord.STATUS_DOING.equals(examRecord.getStatus())) {
            return true;
        }

        // 查询这个认证考试所在考试组的允许考试次数
        List<SubAuthenticatedExamGroup> list = examGroupDao.execute(d -> d.select(Fields.start()
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_GROUP_ID)
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_TIMES)
                        .end())
                .from(SUB_AUTHENTICATED_EXAM_GROUP)
                .where(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID.eq(examId))
                .and(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_TYPE.eq(SubAuthenticatedExamGroup.BUSINESS_TYPE_2))
                .limit(1)
                .fetch(r->{
                    SubAuthenticatedExamGroup examGroup = new SubAuthenticatedExamGroup();
                    examGroup.setExamGroupId(r.getValue(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_GROUP_ID));
                    examGroup.setExamTimes(r.getValue(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_TIMES));
                    return examGroup;
                }));
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(list) || list.get(0).getExamTimes() == null) {
            return true;
        }
        String groupId = list.get(0).getExamGroupId();
        Integer allowExamTimes = list.get(0).getExamTimes();
        // 查询该考试组中的所有认证考试id
        List<String> examIds = examGroupDao.execute(d -> d.select(Fields.start()
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID)
                        .end())
                .from(SUB_AUTHENTICATED_EXAM_GROUP)
                .where(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_GROUP_ID.eq(groupId))
                .and(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_TYPE.eq(SubAuthenticatedExamGroup.BUSINESS_TYPE_2))
                .fetch(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID));
        // 查询受众的ids
        List<String> targetIds = groupExamIdCache.get(groupId+"#"+memberId+"#"+SubAuthenticatedExamGroup.BUSINESS_TYPE_2,()->findTargetIds(examIds, memberId), 60*10);
        // 查询已考次数
        int examTimes = findExamTimes(targetIds, memberId);
        return allowExamTimes > examTimes;
    }

    @Override
    public List<SubAuthenticatedExamGroup> findByGroups(List<String> examGroups) {
        return examGroupDao.execute(d -> d.select(Fields.start()
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_GROUP_ID)
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID)
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_NAME)
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_TYPE)
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.ORDER)
                        .end())
                .from(SUB_AUTHENTICATED_EXAM_GROUP)
                .where(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_GROUP_ID.in(examGroups))
                .fetch(r -> {
                    SubAuthenticatedExamGroup group = new SubAuthenticatedExamGroup();
                    group.setExamGroupId(r.getValue(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_GROUP_ID));
                    group.setBusinessId(r.getValue(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID));
                    group.setBusinessName(r.getValue(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_NAME));
                    group.setBusinessType(r.getValue(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_TYPE));
                    group.setOrder(r.getValue(SUB_AUTHENTICATED_EXAM_GROUP.ORDER));
                    return group;
                }));
    }

    @Override
    @DataSource
    public PagedResult<CertificateRecord> findCertificateRecordPagedResult(Integer page, Integer pageSize, String id,
                                                                           Map<String, Set<String>> grantOrganizationPathMap, Optional<String> name,
                                                                           Optional<String> fullName, Optional<String> organizationId, Optional<Integer> accessType, Optional<String> examName) {

        List<String> ids = examGroupDao.execute(d -> d.select(Fields.start()
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID)
                        .end())
                .from(SUB_AUTHENTICATED_EXAM_GROUP)
                .where(SUB_AUTHENTICATED_EXAM_GROUP.SUB_AUTHENTICATED_ID.eq(id))
                .fetch(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID));
        if (ObjectUtils.isEmpty(ids)) {
            return PagedResult.create(0, Lists.newArrayList());
        }

        List<Condition> conditions = Stream.of(
                Optional.of(ids).map(CERTIFICATE_RECORD.EXAM_ID::in),
                name.map(MEMBER.NAME::eq),
                fullName.map(MEMBER.FULL_NAME::eq),
                accessType.map(CERTIFICATE_RECORD.ACCESS_TYPE::eq),
                examName.map(EXAM.NAME::eq),
                organizationId.map(t -> ORGANIZATION.PATH.startsWith(organizationDao.get(t).getPath()))
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

        generateOrganizationConditions(grantOrganizationPathMap,conditions);

        int count = examDao.execute(d -> d.select(DSL.count(CERTIFICATE_RECORD.ID))
                .from(CERTIFICATE_RECORD)
                .leftJoin(EXAM).on(CERTIFICATE_RECORD.EXAM_ID.eq(EXAM.ID))
                .leftJoin(MEMBER).on(CERTIFICATE_RECORD.MEMBER_ID.eq(MEMBER.ID))
                .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(conditions)
                .fetchOne(DSL.count(CERTIFICATE_RECORD.ID)));

        if (count == 0) {
            return PagedResult.create(count, Lists.newArrayList());
        }

        List<CertificateRecord> certificateRecords = examDao.execute(d -> d.select(Fields.start()
                        .add(CERTIFICATE_RECORD.ID)
                        .add(MEMBER.ID)
                        .add(MEMBER.NAME)
                        .add(MEMBER.FULL_NAME)
                        .add(ORGANIZATION.ID)
                        .add(ORGANIZATION.NAME)
                        .add(CERTIFICATE_RECORD.ACCESS_TYPE)
                        .add(CERTIFICATE_RECORD.ISSUE_TIME)
                        .add(EXAM.ID)
                        .add(EXAM.NAME).end())
                .from(CERTIFICATE_RECORD)
                .leftJoin(EXAM).on(CERTIFICATE_RECORD.EXAM_ID.eq(EXAM.ID))
                .leftJoin(MEMBER).on(CERTIFICATE_RECORD.MEMBER_ID.eq(MEMBER.ID))
                .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(conditions)
                .limit((page - 1) * pageSize, pageSize).fetch(r -> {
                    CertificateRecord certificateRecord = new CertificateRecord();
                    certificateRecord.setId(r.getValue(CERTIFICATE_RECORD.ID));
                    certificateRecord.setAccessType(r.getValue(CERTIFICATE_RECORD.ACCESS_TYPE));
                    certificateRecord.setIssueTime(r.getValue(CERTIFICATE_RECORD.ISSUE_TIME));
                    certificateRecord.setExamId(r.getValue(EXAM.ID));
                    certificateRecord.setMemberId(r.getValue(MEMBER.ID));
                    Exam exam = new Exam();
                    exam.setName(r.getValue(EXAM.NAME));
                    certificateRecord.setExam(exam);
                    Member member = new Member();
                    member.setId(r.getValue(MEMBER.ID));
                    member.setName(r.getValue(MEMBER.NAME));
                    member.setFullName(r.getValue(MEMBER.FULL_NAME));
                    Organization organization = new Organization();
                    organization.setId(r.getValue(ORGANIZATION.ID));
                    organization.setName(r.getValue(ORGANIZATION.NAME));
                    member.setOrganization(organization);
                    certificateRecord.setMember(member);
                    return certificateRecord;
                }));

        List<String> examIds = certificateRecords.stream().map(CertificateRecord::getExamId).distinct().collect(Collectors.toList());
        Map<String, Integer> map = Maps.newHashMap();
        for (int i = 0; i < examIds.size(); i++) {
            String examId = examIds.get(i);
            List<String> memberIds = certificateRecords.stream().filter(r -> examId.equals(r.getExamId())).map(CertificateRecord::getMemberId).distinct().collect(Collectors.toList());
            TableImpl<?> examRegistTable = getTableUtil.getExamRegistTable(getTableUtil.getExamRegistStringTable(examId));
            examRegistCommonDao.execute(e -> e.select(
                            Fields.start()
                                    .add(examRegistTable.field("f_member_id", String.class))
                                    .add(examRegistTable.field("f_top_score", Integer.class))
                                    .end())
                    .from(examRegistTable)
                    .where(examRegistTable.field("f_member_id", String.class).in(memberIds)
                            .and(examRegistTable.field("f_exam_id", String.class).eq(examId)))
                    .fetch(r -> map.put(examId+"#"+r.getValue(examRegistTable.field("f_member_id", String.class)), r.getValue(examRegistTable.field("f_top_score", Integer.class)))));
        }
        certificateRecords.forEach(r-> r.setScore(map.get(r.getExamId()+"#"+r.getMemberId())));
        return PagedResult.create(count, certificateRecords);
    }

    @Override
    public List<Exam> findExamDataBySubId(String id) {
        return examGroupDao.execute(d -> d.select(Fields.start()
                        .add(EXAM.ID)
                        .add(EXAM.NAME)
                        .add(EXAM.START_TIME)
                        .add(EXAM.END_TIME)
                        .end())
                .from(SUB_AUTHENTICATED_EXAM_GROUP)
                .leftJoin(EXAM).on(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID.eq(EXAM.ID))
                .where(SUB_AUTHENTICATED_EXAM_GROUP.SUB_AUTHENTICATED_ID.eq(id))
                .orderBy(SUB_AUTHENTICATED_EXAM_GROUP.CREATE_TIME)
                .fetch(r -> {
                    Exam exam = new Exam();
                    exam.setId(r.getValue(EXAM.ID));
                    exam.setName(r.getValue(EXAM.NAME));
                    exam.setStartTime(r.getValue(EXAM.START_TIME));
                    exam.setEndTime(r.getValue(EXAM.END_TIME));
                    return exam;
                }));
    }

    @Override
    public  Map<String, Object> findFrontExamList(String subId, String contentId, Integer businessType, String currentUserId) {
        // 查询子认证考试组对应类型的考试信息
        List<Exam> list = examGroupDao.execute(d -> d.select(Fields.start()
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_TIMES)
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.ORDER)
                        .add(EXAM.ID)
                        .add(EXAM.NAME)
                        .add(EXAM.START_TIME)
                        .add(EXAM.END_TIME)
                        .end())
                .from(SUB_AUTHENTICATED_EXAM_GROUP)
                .leftJoin(EXAM).on(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID.eq(EXAM.ID))
                .where(SUB_AUTHENTICATED_EXAM_GROUP.SUB_AUTHENTICATED_ID.eq(subId))
                .and(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_GROUP_ID.eq(contentId))
                .and(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_TYPE.eq(businessType))
                .fetch(r -> {
                    Exam exam = new Exam();
                    exam.setId(r.getValue(EXAM.ID));
                    exam.setName(r.getValue(EXAM.NAME));
                    exam.setStartTime(r.getValue(EXAM.START_TIME));
                    exam.setEndTime(r.getValue(EXAM.END_TIME));
                    exam.setAllowExamTimes(r.getValue(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_TIMES));
                    exam.setIndex(r.getValue(SUB_AUTHENTICATED_EXAM_GROUP.ORDER));
                    return exam;
                }));
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        // 查询这个人的受众考试targetIds
        List<String> ids = list.stream().map(Exam::getId).distinct().collect(Collectors.toList());
        List<String> targetIds = groupExamIdCache.get(contentId+"#"+currentUserId+"#"+businessType,()->findTargetIds(ids, currentUserId), 60*10);
        if (CollectionUtils.isEmpty(targetIds)) {
            return new HashMap<>();
        }
        List<Exam> exams = list.stream().filter(e -> targetIds.contains(e.getId())).sorted(Comparator.comparing(Exam::getIndex)).collect(Collectors.toList());
        Map<String, Object> map = Maps.newHashMap();
        map.put("exams",exams);
        // 认证考试，设置了考试组的考试次数，需要查询剩余的次数
        if (SubAuthenticatedExamGroup.BUSINESS_TYPE_2 == businessType
                && !CollectionUtils.isEmpty(exams)
                && exams.get(0).getAllowExamTimes() != null) {
            // 查询考试组已考次数
            int count = findExamTimes(exams.stream().map(Exam::getId).distinct().collect(Collectors.toList()), currentUserId);
            Integer allowExamTimes = exams.get(0).getAllowExamTimes();
            map.put("remainCount",count>allowExamTimes?0:allowExamTimes-count);
        }
        return map;
    }

    private List<String> findTargetIds(List<String> ids, String currentUserId) {
        return examGroupDao.execute(d -> d.selectDistinct(Fields.start()
                        .add(AUDIENCE_OBJECT.TARGET_ID)
                        .end())
                .from(AUDIENCE_OBJECT)
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
                .where(AUDIENCE_OBJECT.TARGET_ID.in(ids))
                .and(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId))
                .and(AUDIENCE_OBJECT.TYPE.eq(AudienceObject.TYPE_EXAM))
                .fetch(AUDIENCE_OBJECT.TARGET_ID));
    }

    @Override
    public  Map<String, String> findTopScoreCertificate(List<String> subAuthenticatedIds, String memberId) {
        Set<String> examIds = new HashSet<>();
        Map<String, List<SubAuthenticatedExamGroup>> subBusinessIdsMap = examGroupDao.execute(
                dslContext -> dslContext.select(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID
                                , SUB_AUTHENTICATED_EXAM_GROUP.EXAM_GROUP_ID)
                        .from(SUB_AUTHENTICATED_EXAM_GROUP)
                        .where(SUB_AUTHENTICATED_EXAM_GROUP.SUB_AUTHENTICATED_ID.in(subAuthenticatedIds))
                        .fetch(r -> {
                            SubAuthenticatedExamGroup group = new SubAuthenticatedExamGroup();
                            group.setExamGroupId(r.getValue(SUB_AUTHENTICATED_EXAM_GROUP.EXAM_GROUP_ID));
                            String examId = r.getValue(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID);
                            group.setBusinessId(examId);
                            examIds.add(examId);
                            return group;
                        }).stream().collect(Collectors.groupingBy(SubAuthenticatedExamGroup::getExamGroupId)));

        Map<String, List<CertificateRecord>> certificateMap = certificateRecordDao.execute(s -> s.select(Fields.start()
                        .add(CERTIFICATE_RECORD.ID)
                        .add(CERTIFICATE_RECORD.EXAM_ID)
                        .add(CERTIFICATE_RECORD.SCORE).end())
                .from(CERTIFICATE_RECORD)
                .where(CERTIFICATE_RECORD.MEMBER_ID.eq(memberId)
                        .and(CERTIFICATE_RECORD.EXAM_ID.in(examIds)))
                .fetch(r -> {
                    CertificateRecord record = new CertificateRecord();
                    record.setId(r.getValue(CERTIFICATE_RECORD.ID));
                    record.setExamId(r.getValue(CERTIFICATE_RECORD.EXAM_ID));
                    record.setScore(r.getValue(CERTIFICATE_RECORD.SCORE));
                    return record;
                }).stream().collect(Collectors.groupingBy(CertificateRecord::getExamId)));

        //map：考试id对应的最高成绩的证书id
        Map<String, String> examTopScoreMap = new HashMap<>();
        for (Map.Entry<String, List<CertificateRecord>> m : certificateMap.entrySet()) {
            CertificateRecord first = m.getValue().stream().sorted(
                    Comparator.comparing(CertificateRecord::getScore).reversed()).collect(Collectors.toList()).get(0);
            examTopScoreMap.put(m.getKey(), first == null ? null : first.getId());
        }
        //map:考试组id对应的证书id
        Map<String, String> subRecordMap = new HashMap<>();
        for (Map.Entry<String, List<SubAuthenticatedExamGroup>> m : subBusinessIdsMap.entrySet()) {
            //获取每个考试组里有最高记录考试证书的那一条
            m.getValue().forEach(s -> {
                String recordId = examTopScoreMap.get(s.getBusinessId());
                if (!StringUtils.isEmpty(recordId) && StringUtils.isEmpty(subRecordMap.get(m.getKey()))) {
                    subRecordMap.put(m.getKey(), recordId);
                }
            });
        }
        return subRecordMap;
    }

    @Override
    @DataSource
    public List<ExamRecord> findExamRecords(List<String> ids, String year) {
        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(ExamRecord.STRING_EXAM_RECORD+"_"+year);
        return examDao.execute(d -> d.select(Fields.start()
                        .add(examRecordTable.field("f_start_time", Long.class))
                        .add(examRecordTable.field("f_exam_times", Integer.class))
                        .add(examRecordTable.field("f_score", Integer.class))
                        .add(examRecordTable.field("f_status", Integer.class))
                        .add(MEMBER.NAME)
                        .add(MEMBER.FULL_NAME)
                        .add(ORGANIZATION.NAME)
                        .add(EXAM.NAME).end())
                .from(examRecordTable)
                .leftJoin(EXAM).on(examRecordTable.field("f_exam_id", String.class).eq(EXAM.ID))
                .leftJoin(MEMBER).on(examRecordTable.field("f_member_id", String.class).eq(MEMBER.ID))
                .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(examRecordTable.field("f_id", String.class).in(ids))
                .fetch(r -> {
                    ExamRecord examRecord = new ExamRecord();
                    examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
                    examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
                    examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
                    examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
                    Member member = new Member();
                    member.setName(r.getValue(MEMBER.NAME));
                    member.setFullName(r.getValue(MEMBER.FULL_NAME));
                    Organization organization = new Organization();
                    organization.setName(r.getValue(ORGANIZATION.NAME));
                    member.setOrganization(organization);
                    examRecord.setMember(member);
                    Exam exam = new Exam();
                    exam.setName(r.getValue(EXAM.NAME));
                    examRecord.setExam(exam);
                    return examRecord;
                }));
    }

    @Override
    @DataSource
    public List<ExamRecord> findExamRecordList(String id, Map<String, Set<String>> grantOrganizationPathMap,
                                               Optional<String> name, Optional<String> fullName,
                                               Optional<String> organizationId,
                                               Optional<String> examName, String year) {

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(ExamRecord.STRING_EXAM_RECORD+"_"+year);

        List<Condition> conditions = Stream.of(
                Optional.of(id).map(SUB_AUTHENTICATED_EXAM_GROUP.SUB_AUTHENTICATED_ID::eq),
                name.map(MEMBER.NAME::eq),
                fullName.map(MEMBER.FULL_NAME::eq),
                examName.map(EXAM.NAME::eq),
                organizationId.map(t -> ORGANIZATION.PATH.startsWith(organizationDao.get(t).getPath()))
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        generateOrganizationConditions(grantOrganizationPathMap,conditions);
        List<ExamRecord> examRecords = examDao.execute(d -> d.select(Fields.start()
                        .add(examRecordTable.field("f_start_time", Long.class))
                        .add(examRecordTable.field("f_exam_times", Integer.class))
                        .add(examRecordTable.field("f_score", Integer.class))
                        .add(examRecordTable.field("f_status", Integer.class))
                        .add(MEMBER.NAME)
                        .add(MEMBER.FULL_NAME)
                        .add(ORGANIZATION.NAME)
                        .add(EXAM.NAME).end())
                .from(examRecordTable)
                .leftJoin(SUB_AUTHENTICATED_EXAM_GROUP).on(examRecordTable.field("f_exam_id", String.class).eq(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID))
                .leftJoin(EXAM).on(examRecordTable.field("f_exam_id", String.class).eq(EXAM.ID))
                .leftJoin(MEMBER).on(examRecordTable.field("f_member_id", String.class).eq(MEMBER.ID))
                .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(conditions)
                .limit(5000).fetch(r -> {
                    ExamRecord examRecord = new ExamRecord();
                    examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
                    examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
                    examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
                    examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
                    Member member = new Member();
                    member.setName(r.getValue(MEMBER.NAME));
                    member.setFullName(r.getValue(MEMBER.FULL_NAME));
                    Organization organization = new Organization();
                    organization.setName(r.getValue(ORGANIZATION.NAME));
                    member.setOrganization(organization);
                    examRecord.setMember(member);
                    Exam exam = new Exam();
                    exam.setName(r.getValue(EXAM.NAME));
                    examRecord.setExam(exam);
                    return examRecord;
                }));
        return examRecords;
    }

    @Override
    public boolean authType(String examId) {
        List<Integer> list = examGroupDao.execute(d -> d.select(Fields.start()
                        .add(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_TYPE)
                        .end())
                .from(SUB_AUTHENTICATED_EXAM_GROUP)
                .where(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID.eq(examId))
                .and(SUB_AUTHENTICATED_EXAM_GROUP.IS_PUBLISH.eq(1))
                .limit(1)
                .fetch(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_TYPE));
        return com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(list) && SubAuthenticatedExamGroup.BUSINESS_TYPE_2 == list.get(0);
    }

    @DataSource
    public int findExamTimes(List<String> examIds, String currentUserId) {
        int examTimes = 0;
        for (int i = 0; i < examIds.size(); i++) {
            String examId = examIds.get(i);
            TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(getTableUtil.getExamRecordStringTable(examId));
            Integer count = examRecordDao.execute(e -> e.select(
                            Fields.start()
                                    .add(DSL.count(examRecordTable.field("f_id", String.class)))
                                    .end())
                    .from(examRecordTable)
                    .where(examRecordTable.field("f_member_id", String.class).eq(currentUserId)
                            .and(examRecordTable.field("f_exam_id", String.class).eq(examId))
                            .and(examRecordTable.field("f_status", Integer.class).notEqual(1))
                    ).fetchOne(DSL.count(examRecordTable.field("f_id", String.class))));
            examTimes = examTimes + count;
        }
        return examTimes;
    }

    @Override
    public String deleteCertificate(String currentUserId, String id, String subId, String reason, String attachmentId, String attachmentName) {
        List<CertificateRecord> list = examGroupDao.execute(d -> d.select(Fields.start()
                        .add(CERTIFICATE_RECORD.ID)
                        .add(CERTIFICATE_RECORD.MEMBER_ID)
                        .add(CERTIFICATE_RECORD.EXAM_ID)
                        .add(CERTIFICATE_RECORD.ISSUE_TIME)
                        .end())
                .from(CERTIFICATE_RECORD)
                .where(CERTIFICATE_RECORD.ID.eq(id))
                .fetch(r -> {
                    CertificateRecord certificateRecord = new CertificateRecord();
                    certificateRecord.setId(r.getValue(CERTIFICATE_RECORD.ID));
                    certificateRecord.setMemberId(r.getValue(CERTIFICATE_RECORD.MEMBER_ID));
                    certificateRecord.setExamId(r.getValue(CERTIFICATE_RECORD.EXAM_ID));
                    certificateRecord.setIssueTime(r.getValue(CERTIFICATE_RECORD.ISSUE_TIME));
                    return certificateRecord;
                }));
        if (CollectionUtils.isEmpty(list)) {
            throw new UnprocessableException(ErrorCode.CertificateDoesNotExist);
        }
        CertificateRecord certificateRecord = list.get(0);
        SubAuthenticatedCancelCertificateRecord record = new SubAuthenticatedCancelCertificateRecord();
        record.forInsert();
        record.setSubAuthenticatedId(subId);
        record.setMemberId(certificateRecord.getMemberId());
        record.setOperatorId(currentUserId);
        record.setExamId(certificateRecord.getExamId());
        record.setCancelTime(System.currentTimeMillis());
        record.setReason(reason);
        record.setPublishTime(certificateRecord.getIssueTime());
        record.setAttachmentId(attachmentId);
        record.setAttachmentName(attachmentName);
        subAuthenticatedCancelCertificateRecordDao.insert(record);
        certificateRecordDao.delete(id);
        deleteDataExamCommonDao.insert(DeleteDataExam.getDeleteData(DeleteDataExam.CERTIFICATE_RECORD,id,""));

        // 异步处理，考试记录表，考试注册表，答题记录表相关字段更新
        messageSender.send(
                MessageTypeContent.CERTIFICATE_NULLIFY,
                MessageHeaderContent.MEMBER_ID, record.getMemberId(),
                MessageHeaderContent.EXAM_ID,record.getExamId());

        return record.getMemberId();
    }

    @Override
    public PagedResult<SubAuthenticatedCancelCertificateRecord> findCancelPagedResult(Integer page, Integer pageSize, String id,
                                                                Map<String, Set<String>> grantOrganizationPathMap, Optional<String> name,
                                                                Optional<String> fullName, Optional<String> organizationId,
                                                                Optional<Long> publishTimeStart, Optional<Long> publishTimeEnd,
                                                                Optional<Long> cancelTimeStart, Optional<Long> cancelTimeEnd) {

        List<Condition> conditions = Stream.of(
                Optional.of(id).map(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.SUB_AUTHENTICATED_ID::eq),
                name.map(MEMBER.NAME::eq),
                fullName.map(MEMBER.FULL_NAME::eq),
                publishTimeStart.map(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.PUBLISH_TIME::ge),
                publishTimeEnd.map(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.PUBLISH_TIME::le),
                cancelTimeStart.map(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.CANCEL_TIME::ge),
                cancelTimeEnd.map(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.CANCEL_TIME::le),
                organizationId.map(t -> ORGANIZATION.PATH.startsWith(organizationDao.get(t).getPath()))
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

        generateOrganizationConditions(grantOrganizationPathMap,conditions);

        int count = examDao.execute(d -> d.select(DSL.count(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.ID))
                .from(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD)
                .leftJoin(MEMBER).on(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.MEMBER_ID.eq(MEMBER.ID))
                .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(conditions)
                .fetchOne(DSL.count(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.ID)));

        if (count == 0) {
            return PagedResult.create(count, Lists.newArrayList());
        }

        com.zxy.product.exam.jooq.tables.Member operatorMember = MEMBER.as("operator");
        List<SubAuthenticatedCancelCertificateRecord> cancelRecords = examDao.execute(d -> d.select(Fields.start()
                        .add(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.ID)
                        .add(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.PUBLISH_TIME)
                        .add(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.CANCEL_TIME)
                        .add(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.REASON)
                        .add(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.ATTACHMENT_ID)
                        .add(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.ATTACHMENT_NAME)
                        .add(MEMBER.ID)
                        .add(MEMBER.NAME)
                        .add(MEMBER.FULL_NAME)
                        .add(ORGANIZATION.ID)
                        .add(ORGANIZATION.NAME)
                        .add(operatorMember.ID)
                        .add(operatorMember.NAME)
                        .add(operatorMember.FULL_NAME).end())
                .from(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD)
                .leftJoin(MEMBER).on(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.MEMBER_ID.eq(MEMBER.ID))
                .leftJoin(operatorMember).on(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.OPERATOR_ID.eq(operatorMember.ID))
                .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(conditions)
                .limit((page - 1) * pageSize, pageSize).fetch(r -> {
                    SubAuthenticatedCancelCertificateRecord cancelRecord = new SubAuthenticatedCancelCertificateRecord();
                    cancelRecord.setId(r.getValue(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.ID));
                    cancelRecord.setPublishTime(r.getValue(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.PUBLISH_TIME));
                    cancelRecord.setCancelTime(r.getValue(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.CANCEL_TIME));
                    cancelRecord.setReason(r.getValue(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.REASON));
                    cancelRecord.setAttachmentId(r.getValue(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.ATTACHMENT_ID));
                    cancelRecord.setAttachmentName(r.getValue(SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.ATTACHMENT_NAME));
                    Member member = new Member();
                    member.setId(r.getValue(MEMBER.ID));
                    member.setName(r.getValue(MEMBER.NAME));
                    member.setFullName(r.getValue(MEMBER.FULL_NAME));
                    Organization organization = new Organization();
                    organization.setId(r.getValue(ORGANIZATION.ID));
                    organization.setName(r.getValue(ORGANIZATION.NAME));
                    member.setOrganization(organization);
                    cancelRecord.setMember(member);
                    Member oMember = new Member();
                    oMember.setId(r.getValue(operatorMember.ID));
                    oMember.setName(r.getValue(operatorMember.NAME));
                    oMember.setFullName(r.getValue(operatorMember.FULL_NAME));
                    cancelRecord.setOperatorMember(oMember);
                    return cancelRecord;
                }));
        return PagedResult.create(count, cancelRecords);

    }

    @Override
    @DataSource
    public PagedResult<ExamRecord> findExamRecordPagedResult(Integer page, Integer pageSize, String id,
                                                             Map<String, Set<String>> grantOrganizationPathMap,
                                                             Optional<String> name, Optional<String> fullName,
                                                             Optional<String> organizationId, Optional<String> examName,
                                                             String year) {

        TableImpl<?> examRecordTable = getTableUtil.getExamRecordTable(ExamRecord.STRING_EXAM_RECORD+"_"+year);

        List<Condition> conditions = Stream.of(
                Optional.of(id).map(SUB_AUTHENTICATED_EXAM_GROUP.SUB_AUTHENTICATED_ID::eq),
                name.map(MEMBER.NAME::eq),
                fullName.map(MEMBER.FULL_NAME::eq),
                examName.map(EXAM.NAME::eq),
                organizationId.map(t -> ORGANIZATION.PATH.startsWith(organizationDao.get(t).getPath()))
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

        generateOrganizationConditions(grantOrganizationPathMap,conditions);

        int count = examDao.execute(d -> d.select(DSL.count(examRecordTable.field("f_id", String.class)))
                .from(examRecordTable)
                .leftJoin(SUB_AUTHENTICATED_EXAM_GROUP).on(examRecordTable.field("f_exam_id", String.class).eq(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID))
                .leftJoin(EXAM).on(examRecordTable.field("f_exam_id", String.class).eq(EXAM.ID))
                .leftJoin(MEMBER).on(examRecordTable.field("f_member_id", String.class).eq(MEMBER.ID))
                .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(conditions)
                .fetchOne(DSL.count(examRecordTable.field("f_id", String.class))));

        if (count == 0) {
            return PagedResult.create(count, Lists.newArrayList());
        }
        List<ExamRecord> examRecords = examDao.execute(d -> d.select(Fields.start()
                        .add(examRecordTable.field("f_id", String.class))
                        .add(examRecordTable.field("f_start_time", Long.class))
                        .add(examRecordTable.field("f_exam_times", Integer.class))
                        .add(examRecordTable.field("f_score", Integer.class))
                        .add(examRecordTable.field("f_status", Integer.class))
                        .add(MEMBER.ID)
                        .add(MEMBER.NAME)
                        .add(MEMBER.FULL_NAME)
                        .add(ORGANIZATION.ID)
                        .add(ORGANIZATION.NAME)
                        .add(EXAM.ID)
                        .add(EXAM.NAME).end())
                .from(examRecordTable)
                .leftJoin(SUB_AUTHENTICATED_EXAM_GROUP).on(examRecordTable.field("f_exam_id", String.class).eq(SUB_AUTHENTICATED_EXAM_GROUP.BUSINESS_ID))
                .leftJoin(EXAM).on(examRecordTable.field("f_exam_id", String.class).eq(EXAM.ID))
                .leftJoin(MEMBER).on(examRecordTable.field("f_member_id", String.class).eq(MEMBER.ID))
                .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(conditions)
                .orderBy(examRecordTable.field("f_exam_id", String.class))
                .limit((page - 1) * pageSize, pageSize).fetch(r -> {
                    ExamRecord examRecord = new ExamRecord();
                    examRecord.setId(r.getValue(examRecordTable.field("f_id", String.class)));
                    examRecord.setStartTime(r.getValue(examRecordTable.field("f_start_time", Long.class)));
                    examRecord.setExamTimes(r.getValue(examRecordTable.field("f_exam_times", Integer.class)));
                    examRecord.setScore(r.getValue(examRecordTable.field("f_score", Integer.class)));
                    examRecord.setStatus(r.getValue(examRecordTable.field("f_status", Integer.class)));
                    Member member = new Member();
                    member.setId(r.getValue(MEMBER.ID));
                    member.setName(DesensitizationUtil.desensitizeEmployeeId(r.getValue(MEMBER.NAME)));
                    member.setFullName(EncryptUtil.aesEncrypt(r.getValue(MEMBER.FULL_NAME),null));
                    Organization organization = new Organization();
                    organization.setId(r.getValue(ORGANIZATION.ID));
                    organization.setName(r.getValue(ORGANIZATION.NAME));
                    member.setOrganization(organization);
                    examRecord.setMember(member);
                    Exam exam = new Exam();
                    exam.setId(r.getValue(EXAM.ID));
                    exam.setName(r.getValue(EXAM.NAME));
                    examRecord.setExam(exam);
                    return examRecord;
                }));
        return PagedResult.create(count, examRecords);
    }


    /**
     * 拼装组织条件
     *
     * @param grantOrganizationMap 组织Map
     * @param conditions
     * return 组织条件
     */
    private void generateOrganizationConditions(Map<String, Set<String>> grantOrganizationMap, List<Condition> conditions) {
        Set<String> organizationIdSet = grantOrganizationMap.get(com.zxy.product.system.entity.Organization.NOT_INCLUDE_KEY);
        Set<String> pathSet = grantOrganizationMap.get(com.zxy.product.system.entity.Organization.INCLUDE_KEY);
        if (!CollectionUtils.isEmpty(pathSet) || !CollectionUtils.isEmpty(organizationIdSet)) {
            Condition condition;
            if (pathSet.isEmpty()) {
                condition = Optional.of(organizationIdSet).map(ORGANIZATION.ID::in).orElse(DSL.trueCondition());
            } else {
                condition = pathSet.stream().map(ORGANIZATION.PATH::startsWith).reduce(DSL::or)
                        .orElse(DSL.trueCondition());
                if (!organizationIdSet.isEmpty()) {
                    condition = condition.or(ORGANIZATION.ID.in(organizationIdSet));
                }
            }
            conditions.add(condition);
        }
    }
}
